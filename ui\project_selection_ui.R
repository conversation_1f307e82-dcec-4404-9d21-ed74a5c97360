# 项目选择页面UI
# 用户首次进入系统时的项目管理页面

project_selection_ui <- function() {
  fluidPage(
    # 自定义CSS样式
    tags$head(
      tags$style(HTML("
        .project-selection-container {
          min-height: 100vh;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 20px;
        }
        
        .project-card {
          background: white;
          border-radius: 15px;
          box-shadow: 0 20px 40px rgba(0,0,0,0.1);
          padding: 40px;
          max-width: 800px;
          width: 100%;
          text-align: center;
        }
        
        .project-title {
          color: #333;
          font-size: 2.5em;
          font-weight: 300;
          margin-bottom: 10px;
        }
        
        .project-subtitle {
          color: #666;
          font-size: 1.2em;
          margin-bottom: 40px;
        }
        
        .project-actions {
          display: flex;
          gap: 20px;
          justify-content: center;
          flex-wrap: wrap;
          margin-bottom: 30px;
        }
        
        .project-btn {
          background: linear-gradient(45deg, #667eea, #764ba2);
          border: none;
          color: white;
          padding: 15px 30px;
          border-radius: 8px;
          font-size: 1.1em;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.3s ease;
          min-width: 200px;
        }
        
        .project-btn:hover {
          transform: translateY(-2px);
          box-shadow: 0 10px 20px rgba(0,0,0,0.2);
          background: linear-gradient(45deg, #5a6fd8, #6a4190);
        }
        
        .project-btn-secondary {
          background: linear-gradient(45deg, #28a745, #20c997);
        }
        
        .project-btn-secondary:hover {
          background: linear-gradient(45deg, #218838, #1ea080);
        }
        
        .project-info {
          background: #f8f9fa;
          border-radius: 8px;
          padding: 20px;
          margin-top: 30px;
          text-align: left;
        }
        
        .project-status {
          display: flex;
          align-items: center;
          gap: 10px;
          margin-bottom: 15px;
        }
        
        .status-indicator {
          width: 12px;
          height: 12px;
          border-radius: 50%;
          background: #dc3545;
        }
        
        .status-indicator.active {
          background: #28a745;
        }
        
        .recent-projects {
          margin-top: 30px;
          text-align: left;
        }
        
        .recent-project-item {
          background: white;
          border: 1px solid #dee2e6;
          border-radius: 8px;
          padding: 15px;
          margin-bottom: 10px;
          cursor: pointer;
          transition: all 0.3s ease;
        }
        
        .recent-project-item:hover {
          border-color: #667eea;
          box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);
        }
        
        .recent-project-name {
          font-weight: 600;
          color: #333;
          margin-bottom: 5px;
        }
        
        .recent-project-path {
          font-size: 0.9em;
          color: #666;
          margin-bottom: 5px;
        }
        
        .recent-project-time {
          font-size: 0.8em;
          color: #999;
        }

        /* 页面切换动画 */
        #project_selection_page, #workspace_page {
          transition: opacity 0.3s ease-in-out;
        }

        .fade-out {
          opacity: 0;
        }

        .fade-in {
          opacity: 1;
        }
      ")),

      # JavaScript代码
      tags$script(HTML("
        // 处理验证信息显示
        Shiny.addCustomMessageHandler('updateValidationInfo', function(data) {
          var infoDiv = document.getElementById('import_validation_info');
          var messageP = document.getElementById('validation_message');

          if (data.show) {
            if (data.type === 'success') {
              infoDiv.style.background = '#d1ecf1';
              infoDiv.style.border = '1px solid #bee5eb';
              infoDiv.style.color = '#0c5460';
            } else if (data.type === 'error') {
              infoDiv.style.background = '#f8d7da';
              infoDiv.style.border = '1px solid #f5c6cb';
              infoDiv.style.color = '#721c24';
            }
            messageP.textContent = data.message;
            infoDiv.style.display = 'block';
          } else {
            infoDiv.style.display = 'none';
          }
        });

        // 处理导入按钮启用/禁用
        Shiny.addCustomMessageHandler('enableImportButton', function(enable) {
          var button = document.getElementById('import_project_btn');
          if (enable) {
            button.disabled = false;
            button.classList.remove('disabled');
          } else {
            button.disabled = true;
            button.classList.add('disabled');
          }
        });

        // 平滑页面切换函数
        function smoothPageTransition(fromPageId, toPageId) {
          var fromPage = document.getElementById(fromPageId);
          var toPage = document.getElementById(toPageId);

          if (fromPage && toPage) {
            // 淡出当前页面
            fromPage.classList.add('fade-out');

            setTimeout(function() {
              fromPage.style.display = 'none';
              fromPage.classList.remove('fade-out');

              // 显示目标页面并淡入
              toPage.style.display = 'block';
              toPage.classList.add('fade-in');
            }, 300);
          }
        }

        // 处理自动进入工作区
        Shiny.addCustomMessageHandler('autoEnterWorkspace', function(data) {
          setTimeout(function() {
            smoothPageTransition('project_selection_page', 'workspace_page');
          }, data.delay || 1000);
        });
      "))
    ),
    
    # 主容器
    div(class = "project-selection-container",
      div(class = "project-card",
        # 标题部分
        h1(class = "project-title", "实验室实时质控系统"),
        p(class = "project-subtitle", "Laboratory Real-time Quality Control System v1.0.0"),
        
        # 当前项目状态
        div(class = "project-status",
          div(class = "status-indicator", id = "project_status_indicator"),
          span("当前状态: ", style = "font-weight: 500;"),
          span(id = "project_status_text", "未打开项目")
        ),
        
        # 项目操作区域
        div(
          # 新建项目区域
          div(style = "margin-bottom: 30px; padding: 20px; background: #f8f9fa; border-radius: 10px;",
            h4("新建项目", style = "color: #333; margin-bottom: 15px;"),
            textInput("new_project_name", "项目名称:",
                     placeholder = "例如: QC_Project_2024", width = "100%"),
            textAreaInput("new_project_description", "项目描述:",
                         placeholder = "可选的项目描述信息", rows = 2, width = "100%"),

            # 自定义路径输入
            textInput("custom_project_path", "项目保存路径:",
                     placeholder = "例如: D:/Projects/QC_Project_2024", width = "100%"),

            actionButton("new_project_btn", "创建项目",
                        class = "project-btn",
                        icon = icon("plus"))
          ),

          # 导入项目区域
          div(style = "padding: 20px; background: #f8f9fa; border-radius: 10px;",
            h4("导入项目", style = "color: #333; margin-bottom: 15px;"),

            # 项目文件夹路径输入
            textInput("import_project_path", "项目文件夹路径:",
                     placeholder = "例如: D:/Projects/ExistingProject", width = "100%"),

            div(id = "import_validation_info",
              style = "margin-top: 15px; padding: 10px; border-radius: 5px; display: none;",
              p(id = "validation_message", style = "margin: 0;")
            ),

            div(style = "margin-top: 15px;",
              actionButton("validate_import_path", "验证路径",
                          class = "project-btn project-btn-secondary",
                          icon = icon("check")),
              actionButton("import_project_btn", "导入项目",
                          class = "project-btn",
                          icon = icon("folder-open"),
                          disabled = TRUE)
            )
          )
        )
      )
    )
  )
}
