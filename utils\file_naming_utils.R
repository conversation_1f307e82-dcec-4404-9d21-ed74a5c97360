# 文件命名工具函数
# 使用数据索引ID来解决文件名冲突问题，替代时间戳后缀方式

# 注意：此文件通过 source() 在 server 环境中加载
# 因此可以直接使用 server 环境中已有的函数，如 get_app_root_path()

#' 基于数据索引ID生成RDS文件名
#'
#' @param file_id 数据索引中的文件ID
#' @return RDS文件的基础名称（不含扩展名）
generate_rds_name_by_id <- function(file_id) {
  if (is.null(file_id) || file_id == "") {
    stop("文件ID不能为空")
  }

  # 直接使用ID作为文件名，添加_spectra后缀
  return(paste0(file_id, "_spectra"))
}

#' 根据文件路径从数据索引中查找文件ID
#'
#' @param file_path 文件路径
#' @param project_name 项目名称（可选）
#' @return 文件ID，如果未找到则返回NULL
find_file_id_by_path <- function(file_path, project_name = NULL) {
  tryCatch({
    # 加载数据索引
    if (!exists("load_data_index")) {
      stop("数据索引管理器未加载")
    }

    index <- load_data_index(project_name)

    if (is.null(index) || length(index$data_files) == 0) {
      return(NULL)
    }

    # 标准化文件路径进行比较
    file_path_norm <- normalizePath(file_path, winslash = "/", mustWork = FALSE)

    # 遍历数据文件查找匹配的路径
    for (file_id in names(index$data_files)) {
      file_record <- index$data_files[[file_id]]
      if (!is.null(file_record$file_path)) {
        record_path_norm <- normalizePath(file_record$file_path, winslash = "/", mustWork = FALSE)

        # 精确匹配
        if (record_path_norm == file_path_norm) {
          return(file_id)
        }

        # 如果精确匹配失败，尝试基于文件名的匹配
        input_basename <- basename(file_path)
        record_basename <- basename(file_record$file_path)
        if (input_basename == record_basename) {
          return(file_id)
        }
      }
    }
    return(NULL)

  }, error = function(e) {
    warning(paste("查找文件ID失败:", e$message))
    return(NULL)
  })
}

#' 生成基于数据索引ID的唯一缓存文件名
#'
#' @param raw_file_path RAW文件的完整路径
#' @param project_name 项目名称（可选）
#' @return 唯一的缓存文件基础名称（不含扩展名）
generate_unique_cache_name <- function(raw_file_path, project_name = NULL) {
  tryCatch({
    # 首先尝试从数据索引中查找文件ID
    file_id <- find_file_id_by_path(raw_file_path, project_name)

    if (!is.null(file_id)) {
      # 使用数据索引ID生成文件名
      return(generate_rds_name_by_id(file_id))
    }

    # 如果在数据索引中找不到，回退到基于文件名的方式
    warning(paste("文件未在数据索引中找到，使用文件名:", raw_file_path))
    base_name <- tools::file_path_sans_ext(basename(raw_file_path))
    return(paste0(base_name, "_spectra"))

  }, error = function(e) {
    # 出错时回退到简单的文件名
    warning(paste("生成唯一缓存名称失败，使用文件名:", e$message))
    base_name <- tools::file_path_sans_ext(basename(raw_file_path))
    return(paste0(base_name, "_spectra"))
  })
}

#' 根据文件ID获取RDS文件路径
#'
#' @param file_id 数据索引中的文件ID
#' @param cache_dir 缓存目录路径
#' @return RDS文件的完整路径
get_rds_file_path_by_id <- function(file_id, cache_dir) {
  if (is.null(file_id) || file_id == "") {
    stop("文件ID不能为空")
  }

  if (!dir.exists(cache_dir)) {
    dir.create(cache_dir, recursive = TRUE, showWarnings = FALSE)
  }

  rds_name <- generate_rds_name_by_id(file_id)
  return(file.path(cache_dir, paste0(rds_name, ".rds")))
}

#' 获取所有现有的RDS文件及其对应的数据索引信息
#'
#' @param cache_dir 缓存目录路径
#' @param project_name 项目名称（可选）
#' @return 包含文件映射关系的数据框
get_existing_rds_mapping <- function(cache_dir, project_name = NULL) {
  if (!dir.exists(cache_dir)) {
    return(data.frame(
      rds_file = character(0),
      file_id = character(0),
      original_path = character(0),
      file_name = character(0),
      stringsAsFactors = FALSE
    ))
  }

  rds_files <- list.files(cache_dir, pattern = "_spectra\\.rds$", full.names = TRUE)

  if (length(rds_files) == 0) {
    return(data.frame(
      rds_file = character(0),
      file_id = character(0),
      original_path = character(0),
      file_name = character(0),
      stringsAsFactors = FALSE
    ))
  }

  # 从RDS文件名提取文件ID
  rds_base_names <- gsub("_spectra\\.rds$", "", basename(rds_files))

  # 尝试加载数据索引来获取完整信息
  mapping <- data.frame(
    rds_file = rds_files,
    file_id = rds_base_names,
    original_path = NA_character_,
    file_name = NA_character_,
    stringsAsFactors = FALSE
  )

  # 如果可以加载数据索引，填充详细信息
  tryCatch({
    if (exists("load_data_index")) {
      index <- load_data_index(project_name)
      if (!is.null(index) && length(index$data_files) > 0) {
        for (i in seq_len(nrow(mapping))) {
          file_id <- mapping$file_id[i]
          if (file_id %in% names(index$data_files)) {
            file_record <- index$data_files[[file_id]]
            mapping$original_path[i] <- file_record$file_path %||% ""
            mapping$file_name[i] <- file_record$file_name %||% ""
          }
        }
      }
    }
  }, error = function(e) {
    # 忽略错误，使用基本信息
  })

  return(mapping)
}

#' 清理孤立的RDS文件（在数据索引中不存在的文件）
#'
#' @param cache_dir 缓存目录路径
#' @param project_name 项目名称（可选）
#' @param dry_run 是否只是预览而不实际删除
#' @return 清理报告
cleanup_orphaned_rds_files <- function(cache_dir, project_name = NULL, dry_run = TRUE) {
  if (!dir.exists(cache_dir)) {
    return(list(
      status = "success",
      message = "缓存目录不存在",
      orphaned_found = 0,
      files_removed = 0
    ))
  }

  rds_files <- list.files(cache_dir, pattern = "_spectra\\.rds$", full.names = TRUE)

  if (length(rds_files) == 0) {
    return(list(
      status = "success",
      message = "没有找到RDS文件",
      orphaned_found = 0,
      files_removed = 0
    ))
  }

  # 获取数据索引
  orphaned_files <- c()
  tryCatch({
    if (exists("load_data_index")) {
      index <- load_data_index(project_name)
      if (!is.null(index) && length(index$data_files) > 0) {
        valid_ids <- names(index$data_files)

        for (rds_file in rds_files) {
          # 从文件名提取ID
          file_id <- gsub("_spectra\\.rds$", "", basename(rds_file))

          # 检查ID是否在数据索引中存在
          if (!file_id %in% valid_ids) {
            orphaned_files <- c(orphaned_files, rds_file)
          }
        }
      } else {
        # 如果没有数据索引，所有文件都被认为是孤立的
        orphaned_files <- rds_files
      }
    } else {
      warning("数据索引管理器未加载，无法检查孤立文件")
      return(list(
        status = "error",
        message = "数据索引管理器未加载",
        orphaned_found = 0,
        files_removed = 0
      ))
    }
  }, error = function(e) {
    warning(paste("检查孤立文件时出错:", e$message))
    return(list(
      status = "error",
      message = paste("检查失败:", e$message),
      orphaned_found = 0,
      files_removed = 0
    ))
  })

  if (length(orphaned_files) == 0) {
    return(list(
      status = "success",
      message = "没有发现孤立文件",
      orphaned_found = 0,
      files_removed = 0
    ))
  }

  if (dry_run) {
    return(list(
      status = "preview",
      message = paste("预览模式：发现", length(orphaned_files), "个孤立文件"),
      orphaned_found = length(orphaned_files),
      files_to_remove = orphaned_files,
      files_removed = 0
    ))
  } else {
    # 实际删除文件
    removed_count <- 0
    for (file in orphaned_files) {
      if (file.remove(file)) {
        removed_count <- removed_count + 1
      }
    }

    return(list(
      status = "success",
      message = paste("成功删除", removed_count, "个孤立文件"),
      orphaned_found = length(orphaned_files),
      files_removed = removed_count
    ))
  }
}

#' 清理mzML文件以节省存储空间
#'
#' @param project_root 项目根目录路径
#' @param project_name 项目名称（可选）
#' @param dry_run 是否只是预览而不实际删除
#' @return 清理报告
cleanup_mzml_files <- function(project_root, project_name = NULL, dry_run = TRUE) {
  mzml_dir <- file.path(project_root, "data", "mzML")
  cache_dir <- file.path(project_root, "data", "cache", "spectra_v2")

  if (!dir.exists(mzml_dir)) {
    return(list(
      status = "success",
      message = "mzML目录不存在",
      files_found = 0,
      files_removed = 0
    ))
  }

  if (!dir.exists(cache_dir)) {
    return(list(
      status = "warning",
      message = "缓存目录不存在，不建议删除mzML文件",
      files_found = 0,
      files_removed = 0
    ))
  }

  # 获取所有mzML文件
  mzml_files <- list.files(mzml_dir, pattern = "\\.mzML$", full.names = TRUE, ignore.case = TRUE)

  if (length(mzml_files) == 0) {
    return(list(
      status = "success",
      message = "没有找到mzML文件",
      files_found = 0,
      files_removed = 0
    ))
  }

  # 获取所有RDS文件
  rds_files <- list.files(cache_dir, pattern = "_spectra\\.rds$", full.names = TRUE)

  # 获取数据索引以建立mzML到RDS的映射关系
  files_to_remove <- c()
  total_size <- 0

  tryCatch({
    if (exists("load_data_index")) {
      index <- load_data_index(project_name)
      if (!is.null(index) && length(index$data_files) > 0) {

        for (mzml_file in mzml_files) {
          # 标准化mzML文件路径
          mzml_path_norm <- normalizePath(mzml_file, winslash = "/", mustWork = FALSE)

          # 查找对应的原始RAW文件路径
          raw_path <- gsub("\\.mzML$", ".raw", mzml_path_norm, ignore.case = TRUE)

          # 在数据索引中查找对应的文件ID
          file_id <- NULL
          for (id in names(index$data_files)) {
            file_record <- index$data_files[[id]]
            if (!is.null(file_record$file_path)) {
              record_path_norm <- normalizePath(file_record$file_path, winslash = "/", mustWork = FALSE)
              if (record_path_norm == raw_path) {
                file_id <- id
                break
              }
            }
          }

          # 如果找到对应的文件ID，检查是否有对应的RDS文件
          if (!is.null(file_id)) {
            expected_rds_name <- paste0(file_id, "_spectra.rds")
            expected_rds_path <- file.path(cache_dir, expected_rds_name)

            if (file.exists(expected_rds_path)) {
              files_to_remove <- c(files_to_remove, mzml_file)
              file_size <- file.info(mzml_file)$size
              if (!is.na(file_size)) {
                total_size <- total_size + file_size
              }
            }
          }
        }
      }
    }
  }, error = function(e) {
    warning(paste("检查mzML文件时出错:", e$message))
    # 回退到简单的文件名匹配
    rds_base_names <- gsub("_spectra\\.rds$", "", basename(rds_files))

    for (mzml_file in mzml_files) {
      mzml_base <- tools::file_path_sans_ext(basename(mzml_file))

      # 检查是否有对应的RDS文件
      has_rds <- any(grepl(paste0("(^|_)", mzml_base, "(_|$)"), rds_base_names))

      if (has_rds) {
        files_to_remove <- c(files_to_remove, mzml_file)
        file_size <- file.info(mzml_file)$size
        if (!is.na(file_size)) {
          total_size <- total_size + file_size
        }
      }
    }
  })

  if (length(files_to_remove) == 0) {
    return(list(
      status = "success",
      message = "没有找到可以安全删除的mzML文件",
      files_found = length(mzml_files),
      files_removed = 0,
      space_saved = 0
    ))
  }

  if (dry_run) {
    return(list(
      status = "preview",
      message = paste("预览模式：发现", length(files_to_remove), "个可删除的mzML文件"),
      files_found = length(mzml_files),
      files_to_remove = files_to_remove,
      files_removed = 0,
      potential_space_saved = total_size
    ))
  } else {
    # 实际删除文件
    removed_count <- 0
    actual_size_saved <- 0

    for (file in files_to_remove) {
      file_size <- file.info(file)$size
      if (file.remove(file)) {
        removed_count <- removed_count + 1
        if (!is.na(file_size)) {
          actual_size_saved <- actual_size_saved + file_size
        }
      }
    }

    return(list(
      status = "success",
      message = paste("成功删除", removed_count, "个mzML文件"),
      files_found = length(mzml_files),
      files_removed = removed_count,
      space_saved = actual_size_saved
    ))
  }
}
