# Utils层 - 项目管理（重构版）
# 基于文件夹的项目管理，项目以文件夹形式存在

# 定义默认项目根目录
if (!exists("PROJECTS_ROOT")) {
  if (exists("GLOBAL_CONFIG") && !is.null(GLOBAL_CONFIG$app_root)) {
    PROJECTS_ROOT <- file.path(dirname(GLOBAL_CONFIG$app_root), "projects")
  } else {
    PROJECTS_ROOT <- file.path(dirname(getwd()), "projects")
  }
}

# 项目路径映射文件（存储在软件安装路径的全局配置目录）
if (exists("GLOBAL_CONFIG") && !is.null(GLOBAL_CONFIG$global_config_path)) {
  PROJECTS_MAP_FILE <- file.path(GLOBAL_CONFIG$global_config_path, "projects_map.json")
} else {
  PROJECTS_MAP_FILE <- file.path(getwd(), "data", "config", "projects_map.json")
}

# 加载项目路径映射
load_projects_map <- function() {
  if (file.exists(PROJECTS_MAP_FILE)) {
    tryCatch({
      json_content <- readLines(PROJECTS_MAP_FILE, warn = FALSE)
      jsonlite::fromJSON(json_content)
    }, error = function(e) {
      log_warning("项目路径映射文件损坏，使用空映射")
      list()
    })
  } else {
    list()
  }
}

# 保存项目路径映射
save_projects_map <- function(projects_map) {
  dir.create(dirname(PROJECTS_MAP_FILE), recursive = TRUE, showWarnings = FALSE)
  json_data <- jsonlite::toJSON(projects_map, auto_unbox = TRUE, pretty = TRUE)
  writeLines(json_data, PROJECTS_MAP_FILE, useBytes = TRUE)
}

# 检查项目是否存在
project_exists <- function(project_name) {
  projects_map <- load_projects_map()
  
  # 检查是否在映射中
  if (project_name %in% names(projects_map)) {
    return(dir.exists(projects_map[[project_name]]))
  }
  
  # 检查默认路径
  default_path <- file.path(PROJECTS_ROOT, project_name)
  return(dir.exists(default_path))
}

# 获取项目路径
get_project_path <- function(project_name) {
  projects_map <- load_projects_map()
  
  # 检查是否在映射中
  if (project_name %in% names(projects_map)) {
    return(projects_map[[project_name]])
  }
  
  # 返回默认路径
  return(file.path(PROJECTS_ROOT, project_name))
}

# 添加项目路径映射
add_project_mapping <- function(project_name, project_path) {
  projects_map <- load_projects_map()
  projects_map[[project_name]] <- project_path
  save_projects_map(projects_map)
  log_info(paste("项目路径映射已添加:", project_name, "->", project_path))
}

# 删除项目路径映射
remove_project_mapping <- function(project_name) {
  projects_map <- load_projects_map()
  if (project_name %in% names(projects_map)) {
    projects_map[[project_name]] <- NULL
    save_projects_map(projects_map)
    log_info(paste("项目路径映射已删除:", project_name))
  }
}

# 创建新项目
create_project <- function(project_name, description = NULL, custom_path = NULL) {
  if (is.null(project_name) || project_name == "") stop("项目名称不能为空")
  
  # 确定项目路径
  if (!is.null(custom_path) && custom_path != "") {
    # 使用用户选择的自定义路径
    project_path <- file.path(custom_path, project_name)
    if (dir.exists(project_path)) stop(paste("项目路径已存在:", project_path))
  } else {
    # 使用默认路径
    project_path <- get_project_path(project_name)
    if (project_exists(project_name)) stop(paste("项目", project_name, "已存在"))
  }
  
  # 创建简化的项目目录结构
  dirs <- c(
    project_path,
    file.path(project_path, "data"),           # 数据目录
    file.path(project_path, "results"),        # 结果目录
    file.path(project_path, "reports"),        # 报告目录
    file.path(project_path, "config")          # 配置目录
  )
  for (dir in dirs) {
    if (!dir.exists(dir)) dir.create(dir, recursive = TRUE)
  }

  # 设置项目路径并切换工作目录
  set_project_root_path(project_path)
  
  # 创建项目配置文件
  project_config <- list(
    name = project_name,
    path = project_path,
    created_time = as.character(Sys.time()),
    description = ifelse(is.null(description), "新建项目", description),
    status = "active",
    data_files = list(),
    monitor_config = list(),
    instrument_info = list(),
    summary = list()
  )
  
  # 只保存标准的project.json文件
  config_file <- get_project_config_path()
  json_config <- jsonlite::toJSON(project_config, auto_unbox = TRUE, pretty = TRUE)
  writeLines(json_config, config_file, useBytes = TRUE)
  
  # 如果是自定义路径，添加到映射中
  if (!is.null(custom_path) && custom_path != "") {
    add_project_mapping(project_name, project_path)
  }
  
  log_info(paste("项目创建成功:", project_name, "路径:", project_path))
  return(list(success = TRUE, project = project_config))
}

# 加载项目配置
load_project_config <- function(project_name) {
  if (!project_exists(project_name)) stop(paste("项目不存在:", project_name))
  config_file <- file.path(get_project_path(project_name), "config", paste0(project_name, ".json"))
  if (!file.exists(config_file)) stop(paste("项目配置文件不存在:", config_file))
  json_content <- readLines(config_file, warn = FALSE)
  config <- jsonlite::fromJSON(json_content)
  log_info(paste("项目配置已加载:", project_name))
  return(config)
}

# 保存项目配置
save_project_config <- function(project_name, config) {
  if (!project_exists(project_name)) stop(paste("项目不存在:", project_name))
  config_file <- file.path(get_project_path(project_name), "config", paste0(project_name, ".json"))
  json_config <- jsonlite::toJSON(config, auto_unbox = TRUE, pretty = TRUE)
  writeLines(json_config, config_file, useBytes = TRUE)
  log_info(paste("项目配置已保存:", project_name))
}

# 获取项目配置（别名）
get_project_config <- function(project_name) {
  load_project_config(project_name)
}

# 删除项目
delete_project <- function(project_name) {
  if (!project_exists(project_name)) stop(paste("项目不存在:", project_name))
  project_path <- get_project_path(project_name)
  files <- list.files(project_path, recursive = TRUE, all.files = TRUE)
  if (length(files) > 0) warning(paste("项目文件夹不为空，将删除所有内容:", project_path))
  unlink(project_path, recursive = TRUE)
  
  # 删除项目路径映射
  remove_project_mapping(project_name)
  
  log_info(paste("项目已删除:", project_name))
  return(list(success = TRUE))
}

# 导出项目（复制整个项目文件夹）
export_project <- function(project_name, export_path = NULL) {
  if (!project_exists(project_name)) stop(paste("项目不存在:", project_name))
  project_path <- get_project_path(project_name)
  if (is.null(export_path)) {
    export_dir <- file.path(GLOBAL_CONFIG$data_path, "exports")
    if (!dir.exists(export_dir)) dir.create(export_dir, recursive = TRUE)
    export_path <- file.path(export_dir, paste0(project_name, "_", format(Sys.time(), "%Y%m%d_%H%M%S")))
  }
  if (dir.exists(export_path)) stop(paste("导出路径已存在:", export_path))
  file.copy(project_path, dirname(export_path), recursive = TRUE)
  file.rename(file.path(dirname(export_path), basename(project_path)), export_path)
  log_info(paste("项目已导出:", export_path))
  return(list(success = TRUE, path = export_path))
}

# 导入项目（注册项目路径，不复制文件）
import_project <- function(import_folder_path) {
  if (!dir.exists(import_folder_path)) stop("导入文件夹不存在")
  
  # 查找项目配置文件
  config_dir <- file.path(import_folder_path, "config")
  if (!dir.exists(config_dir)) stop("导入文件夹不是有效的项目文件夹（缺少config目录）")
  
  config_files <- list.files(config_dir, pattern = "\\.json$", full.names = TRUE)
  if (length(config_files) == 0) stop("导入文件夹不是有效的项目文件夹（缺少配置文件）")
  
  # 尝试读取第一个配置文件
  config_file <- config_files[1]
  project_config <- tryCatch({
    json_content <- readLines(config_file, warn = FALSE)
    jsonlite::fromJSON(json_content)
  }, error = function(e) stop("项目配置文件格式错误"))
  
  project_name <- project_config$name
  if (is.null(project_name) || project_name == "") stop("项目配置文件中缺少项目名称")
  
  # 检查项目是否已经注册
  if (project_exists(project_name)) {
    log_warning(paste("项目", project_name, "已存在，将更新路径映射"))
  }
  
  # 设置项目路径并切换工作目录
  set_project_root_path(import_folder_path)

  # 更新项目配置
  project_config$path <- normalizePath(import_folder_path)
  project_config$import_time <- as.character(Sys.time())
  project_config$import_type <- "external"  # 标记为外部导入项目

  # 只保存标准的project.json文件
  standard_config_file <- get_project_config_path()
  json_config <- jsonlite::toJSON(project_config, auto_unbox = TRUE, pretty = TRUE)
  writeLines(json_config, standard_config_file, useBytes = TRUE)
  
  # 添加到项目路径映射
  add_project_mapping(project_name, normalizePath(import_folder_path))
  
  log_info(paste("项目导入成功:", project_name, "路径:", normalizePath(import_folder_path)))
  return(list(success = TRUE, project = project_config))
}

# 获取当前项目名称
get_current_project <- function() {
  # 尝试从当前工作目录推断项目名称
  current_dir <- getwd()

  # 检查当前目录是否包含项目配置文件
  config_file <- file.path(current_dir, "config", "project.json")
  if (file.exists(config_file)) {
    tryCatch({
      config <- jsonlite::fromJSON(config_file)
      return(config$name)
    }, error = function(e) {
      log_warning("读取项目配置文件失败")
    })
  }

  # 检查config目录下是否有项目配置文件
  config_dir <- file.path(current_dir, "config")
  if (dir.exists(config_dir)) {
    json_files <- list.files(config_dir, pattern = "\\.json$", full.names = TRUE)
    for (json_file in json_files) {
      if (basename(json_file) != "project.json") {
        tryCatch({
          config <- jsonlite::fromJSON(json_file)
          if (!is.null(config$name)) {
            return(config$name)
          }
        }, error = function(e) {
          # 继续尝试下一个文件
        })
      }
    }
  }

  # 从目录名推断项目名称
  project_name <- basename(current_dir)
  return(project_name)
}

# 获取项目信息（用于展示）
get_project_info <- function(project_name = NULL) {
  if (is.null(project_name)) {
    project_name <- get_current_project()
  }

  if (!project_exists(project_name)) return(list(error = "项目不存在"))

  info <- tryCatch({
    config <- load_project_config(project_name)
    project_path <- get_project_path(project_name)

    # 安全地获取文件列表
    raw_data_dir <- file.path(project_path, "raw_data")
    results_dir <- file.path(project_path, "results")
    reports_dir <- file.path(project_path, "reports")

    raw_data_files <- if (dir.exists(raw_data_dir)) list.files(raw_data_dir, recursive = TRUE) else character()
    result_files <- if (dir.exists(results_dir)) list.files(results_dir, recursive = TRUE) else character()
    report_files <- if (dir.exists(reports_dir)) list.files(reports_dir, recursive = TRUE) else character()

    # 安全地计算总大小
    all_files <- list.files(project_path, recursive = TRUE, full.names = TRUE)
    total_size <- if (length(all_files) > 0) {
      file_sizes <- file.size(all_files)
      sum(file_sizes[!is.na(file_sizes)])
    } else {
      0
    }

    list(
      name = config$name,
      path = normalizePath(project_path),  # 使用实际项目路径
      created_time = config$created_time,
      description = config$description,
      status = config$status,
      raw_data_count = length(raw_data_files),
      result_count = length(result_files),
      report_count = length(report_files),
      total_size_mb = round(total_size / 1024 / 1024, 2),
      summary = config$summary
    )
  }, error = function(e) list(error = paste("获取项目信息失败:", e$message)))
  return(info)
}

# 更新项目摘要信息
update_project_summary <- function(project_name, summary_data) {
  if (!project_exists(project_name)) stop(paste("项目不存在:", project_name))
  config <- load_project_config(project_name)
  config$summary <- summary_data
  save_project_config(project_name, config)
  log_info(paste("项目摘要已更新:", project_name))
}

# 获取数据列表（使用路径管理器）
get_data_list <- function(project_name) {
  tryCatch({
    # 使用路径管理器获取数据文件路径
    data_file <- get_data_list_path()

    if (file.exists(data_file)) {
      data_list <- read.csv(data_file, stringsAsFactors = FALSE, fileEncoding = "UTF-8")
      return(data_list)
    }

    # 如果没有保存的数据文件，返回空数据框
    return(data.frame(
      文件名 = character(),
      路径 = character(),
      大小 = character(),
      状态 = character(),
      类型 = character(),
      样本类型 = character(),
      修改时间 = character(),
      备注 = character(),
      stringsAsFactors = FALSE
    ))

  }, error = function(e) {
    log_error(paste("获取数据列表失败:", e$message))
    return(data.frame(
      文件名 = "加载失败",
      路径 = "",
      大小 = "",
      状态 = "错误",
      类型 = "",
      样本类型 = "",
      修改时间 = "",
      备注 = paste("错误:", e$message),
      stringsAsFactors = FALSE
    ))
  })
}

# 删除选中数据
remove_selected_data <- function(project_name, selected_indices) {
  if (!project_exists(project_name)) return(FALSE)
  
  tryCatch({
    project_path <- get_project_path(project_name)
    raw_data_dir <- file.path(project_path, "raw_data")
    
    if (!dir.exists(raw_data_dir)) return(FALSE)
    
    files <- list.files(raw_data_dir, full.names = TRUE)
    if (length(files) == 0) return(FALSE)
    
    # 删除选中的文件
    for (index in selected_indices) {
      if (index <= length(files)) {
        file.remove(files[index])
        log_info(paste("删除文件:", basename(files[index])))
      }
    }
    
    return(TRUE)
  }, error = function(e) {
    log_error(paste("删除数据失败:", e$message))
    return(FALSE)
  })
}

# 获取监控结果
get_monitoring_results <- function(project_name) {
  if (!project_exists(project_name)) return(NULL)
  
  tryCatch({
    project_path <- get_project_path(project_name)
    results_dir <- file.path(project_path, "results")
    
    if (!dir.exists(results_dir)) {
      return(data.frame(时间 = character(), 类型 = character(), 值 = numeric()))
    }
    
    # 查找结果文件
    result_files <- list.files(results_dir, pattern = "\\.csv$", full.names = TRUE)
    if (length(result_files) == 0) {
      return(data.frame(时间 = character(), 类型 = character(), 值 = numeric()))
    }
    
    # 读取最新的结果文件
    latest_file <- result_files[length(result_files)]
    results <- read.csv(latest_file, stringsAsFactors = FALSE)
    
    return(results)
  }, error = function(e) {
    log_error(paste("获取监控结果失败:", e$message))
    return(data.frame(时间 = character(), 类型 = character(), 值 = numeric()))
  })
}

# 生成统计报告
generate_statistics_report <- function(project_name) {
  if (!project_exists(project_name)) return("项目不存在")
  
  tryCatch({
    project_info <- get_project_info(project_name)
    if (!is.null(project_info$error)) {
      return(project_info$error)
    }
    
    results <- get_monitoring_results(project_name)
    
    report <- paste(
      "=== 项目统计报告 ===\n",
      "项目名称:", project_info$name, "\n",
      "创建时间:", project_info$created_time, "\n",
      "描述:", project_info$description, "\n",
      "状态:", project_info$status, "\n",
      "原始数据文件数:", project_info$raw_data_count, "\n",
      "结果文件数:", project_info$result_count, "\n",
      "报告文件数:", project_info$report_count, "\n",
      "总大小:", project_info$total_size_mb, "MB\n\n"
    )
    
    if (nrow(results) > 0) {
      report <- paste0(report, "=== 监控结果统计 ===\n")
      for (type in unique(results$类型)) {
        type_data <- results[results$类型 == type, ]
        report <- paste0(report, 
                        type, "监控:\n",
                        "  数据点数: ", nrow(type_data), "\n",
                        "  平均值: ", round(mean(type_data$值, na.rm = TRUE), 4), "\n",
                        "  标准差: ", round(sd(type_data$值, na.rm = TRUE), 4), "\n",
                        "  最小值: ", round(min(type_data$值, na.rm = TRUE), 4), "\n",
                        "  最大值: ", round(max(type_data$值, na.rm = TRUE), 4), "\n\n")
      }
    } else {
      report <- paste0(report, "暂无监控结果数据\n")
    }
    
    return(report)
  }, error = function(e) {
    log_error(paste("生成统计报告失败:", e$message))
    return("无法生成统计报告")
  })
}

# 获取图表数据
get_plot_data <- function(project_name, plot_type) {
  if (!project_exists(project_name)) return(data.frame(time = numeric(), value = numeric()))
  
  tryCatch({
    results <- get_monitoring_results(project_name)
    if (nrow(results) == 0) {
      return(data.frame(time = numeric(), value = numeric()))
    }
    
    # 根据图表类型过滤数据
    if (plot_type == "tic") {
      plot_data <- results[results$类型 == "TIC", ]
    } else if (plot_type == "bpc") {
      plot_data <- results[results$类型 == "BPC", ]
    } else if (plot_type == "mz") {
      plot_data <- results[results$类型 == "m/z", ]
    } else if (plot_type == "eic") {
      plot_data <- results[results$类型 == "EIC", ]
    } else {
      return(data.frame(time = numeric(), value = numeric()))
    }
    
    if (nrow(plot_data) == 0) {
      return(data.frame(time = numeric(), value = numeric()))
    }
    
    # 转换时间格式
    plot_data$time <- as.numeric(as.POSIXct(plot_data$时间))
    plot_data$value <- as.numeric(plot_data$值)
    
    return(plot_data[, c("time", "value")])
  }, error = function(e) {
    log_error(paste("获取图表数据失败:", e$message))
    return(data.frame(time = numeric(), value = numeric()))
  })
}

# 启动文件夹监控
start_folder_monitoring <- function(monitor_path, include_subfolders = FALSE, file_pattern = "*.raw") {
  tryCatch({
    log_info(paste("启动文件夹监控:", monitor_path))
    
    # 这里应该实现实际的文件夹监控逻辑
    # 目前只是记录日志
    monitoring_config <- list(
      path = monitor_path,
      include_subfolders = include_subfolders,
      file_pattern = file_pattern,
      start_time = Sys.time()
    )
    
    # 保存监控配置到项目目录
    project_root <- get_project_root_path()
    if (!is.null(project_root)) {
      config_file <- file.path(project_root, "config", "folder_monitoring.json")
      dir.create(dirname(config_file), recursive = TRUE, showWarnings = FALSE)
      json_config <- jsonlite::toJSON(monitoring_config, auto_unbox = TRUE, pretty = TRUE)
      writeLines(json_config, config_file, useBytes = TRUE)
    }
    
    return(TRUE)
  }, error = function(e) {
    log_error(paste("启动文件夹监控失败:", e$message))
    return(FALSE)
  })
}

# 生成并导出报告
generate_and_export_report <- function(project_name) {
  if (!project_exists(project_name)) {
    return(list(success = FALSE, message = "项目不存在"))
  }
  
  tryCatch({
    project_path <- get_project_path(project_name)
    reports_dir <- file.path(project_path, "reports")
    
    if (!dir.exists(reports_dir)) {
      dir.create(reports_dir, recursive = TRUE)
    }
    
    # 生成报告内容
    report_content <- generate_statistics_report(project_name)
    
    # 创建报告文件名
    timestamp <- format(Sys.time(), "%Y%m%d_%H%M%S")
    report_filename <- paste0(project_name, "_report_", timestamp, ".txt")
    report_path <- file.path(reports_dir, report_filename)
    
    # 写入报告文件
    writeLines(report_content, report_path, useBytes = TRUE)
    
    log_info(paste("报告已生成:", report_path))
    return(list(success = TRUE, file = report_filename))
    
  }, error = function(e) {
    log_error(paste("生成报告失败:", e$message))
    return(list(success = FALSE, message = e$message))
  })
}

# 处理上传的文件
process_uploaded_files <- function(file_paths, project_name) {
  if (!project_exists(project_name)) {
    return(list(success = FALSE, message = "项目不存在"))
  }
  
  tryCatch({
    project_path <- get_project_path(project_name)
    results_dir <- file.path(project_path, "results")
    
    if (!dir.exists(results_dir)) {
      dir.create(results_dir, recursive = TRUE)
    }
    
    # 创建结果数据框
    results_data <- data.frame(
      时间 = character(),
      类型 = character(),
      值 = numeric(),
      stringsAsFactors = FALSE
    )
    
    # 处理每个文件
    for (file_path in file_paths) {
      file_name <- basename(file_path)
      log_info(paste("处理文件:", file_name))
      
      # 这里应该实现实际的数据处理逻辑
      # 目前只是创建模拟数据
      current_time <- Sys.time()
      
      # 生成模拟的监控数据
      if (file.exists(file_path)) {
        # 读取文件大小作为基础数据
        file_size <- file.size(file_path)
        
        # 生成TIC数据
        tic_data <- data.frame(
          时间 = rep(as.character(current_time), 5),
          类型 = rep("TIC", 5),
          值 = rnorm(5, mean = file_size / 1000, sd = file_size / 10000),
          stringsAsFactors = FALSE
        )
        
        # 生成BPC数据
        bpc_data <- data.frame(
          时间 = rep(as.character(current_time), 5),
          类型 = rep("BPC", 5),
          值 = rnorm(5, mean = file_size / 2000, sd = file_size / 20000),
          stringsAsFactors = FALSE
        )
        
        # 合并数据
        file_results <- rbind(tic_data, bpc_data)
        results_data <- rbind(results_data, file_results)
      }
    }
    
    # 保存结果
    if (nrow(results_data) > 0) {
      timestamp <- format(Sys.time(), "%Y%m%d_%H%M%S")
      results_filename <- paste0("results_", timestamp, ".csv")
      results_path <- file.path(results_dir, results_filename)
      
      write.csv(results_data, results_path, row.names = FALSE, fileEncoding = "UTF-8")
      log_info(paste("结果已保存:", results_path))
    }
    
    return(list(success = TRUE, message = "文件处理完成"))
    
  }, error = function(e) {
    log_error(paste("处理上传文件失败:", e$message))
    return(list(success = FALSE, message = e$message))
  })
}

# 启动监控任务
start_monitoring_tasks <- function(monitor_config) {
  tryCatch({
    log_info("启动监控任务")
    
    # 验证配置
    if (is.null(monitor_config$types) || length(monitor_config$types) == 0) {
      return(list(success = FALSE, message = "未选择监控类型"))
    }
    
    if (is.null(monitor_config$project) || monitor_config$project == "") {
      return(list(success = FALSE, message = "未选择项目"))
    }
    
    # 检查是否需要监控离子
    needs_ions <- any(c("mz", "rsd", "eic") %in% monitor_config$types)
    if (needs_ions && (is.null(monitor_config$ions) || length(monitor_config$ions) == 0)) {
      return(list(success = FALSE, message = "选择的监控类型需要监控离子配置"))
    }
    
    # 保存监控配置
    project_path <- get_project_path(monitor_config$project)
    config_dir <- file.path(project_path, "config")
    if (!dir.exists(config_dir)) {
      dir.create(config_dir, recursive = TRUE)
    }
    
    monitor_config_file <- file.path(config_dir, "monitor_config.json")
    json_config <- jsonlite::toJSON(monitor_config, auto_unbox = TRUE, pretty = TRUE)
    writeLines(json_config, monitor_config_file, useBytes = TRUE)
    
    # 这里应该启动实际的监控任务
    # 目前只是记录日志
    log_info(paste("监控任务已启动，项目:", monitor_config$project))
    log_info(paste("监控类型:", paste(monitor_config$types, collapse = ", ")))
    
    if (needs_ions) {
      log_info(paste("监控离子数量:", length(monitor_config$ions)))
    }
    
    return(list(success = TRUE, message = "监控任务启动成功"))
    
  }, error = function(e) {
    log_error(paste("启动监控任务失败:", e$message))
    return(list(success = FALSE, message = paste("启动监控任务失败:", e$message)))
  })
}

# 停止监控任务
stop_monitoring_tasks <- function() {
  tryCatch({
    log_info("停止监控任务")
    
    # 这里应该停止实际的监控任务
    # 目前只是记录日志
    
    # 清理监控配置（从项目目录）
    project_root <- get_project_root_path()
    if (!is.null(project_root)) {
      config_file <- file.path(project_root, "config", "folder_monitoring.json")
      if (file.exists(config_file)) {
        file.remove(config_file)
      }
    }
    
    return(list(success = TRUE, message = "监控任务已停止"))
    
  }, error = function(e) {
    log_error(paste("停止监控任务失败:", e$message))
    return(list(success = FALSE, message = paste("停止监控任务失败:", e$message)))
  })
} 