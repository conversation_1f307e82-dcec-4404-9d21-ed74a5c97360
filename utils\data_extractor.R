# 数据提取器
# 从Spectra对象中提取spectraData和peaksData，按MS级别组织

# 加载必要的包
load_extraction_packages <- function() {
  required_packages <- c("Spectra", "MsExperiment", "dplyr")
  
  for (pkg in required_packages) {
    if (!requireNamespace(pkg, quietly = TRUE)) {
      stop(paste("Required package", pkg, "is not installed. Please install it first."))
    }
  }
  
  library(Spectra)
  library(MsExperiment)
  library(dplyr)
}

# 从单个_spectra.rds文件提取数据
extract_spectra_data <- function(rds_file_path, file_id = NULL) {
  tryCatch({
    cat("提取数据:", basename(rds_file_path), "\n")
    
    # 读取对象
    data_obj <- readRDS(rds_file_path)
    
    # 检查对象类型并提取Spectra对象
    if (inherits(data_obj, "MsExperiment")) {
      spectra_obj <- MsExperiment::spectra(data_obj)
    } else if (inherits(data_obj, "Spectra")) {
      spectra_obj <- data_obj
    } else {
      stop("不支持的对象类型:", class(data_obj)[1])
    }
    
    # 基本信息
    total_spectra <- length(spectra_obj)
    if (total_spectra == 0) {
      stop("Spectra对象为空")
    }
    
    # 获取MS级别
    ms_levels <- Spectra::msLevel(spectra_obj)
    ms1_indices <- which(ms_levels == 1)
    ms2_indices <- which(ms_levels == 2)
    
    cat("  - 总谱图数:", total_spectra, "\n")
    cat("  - MS1谱图数:", length(ms1_indices), "\n")
    cat("  - MS2谱图数:", length(ms2_indices), "\n")
    
    # 提取文件基本信息
    file_info <- list(
      file_name = basename(rds_file_path),
      file_path = rds_file_path,
      total_spectra = total_spectra,
      ms1_count = length(ms1_indices),
      ms2_count = length(ms2_indices)
    )
    
    # 如果提供了file_id，使用它；否则从RDS文件名提取或生成一个临时ID
    if (is.null(file_id)) {
      # 尝试从RDS文件名提取数据索引ID
      rds_base_name <- gsub("_spectra\\.rds$", "", basename(rds_file_path))
      if (grepl("^file_\\d{8}_\\d{6}_\\d+$", rds_base_name)) {
        file_id <- rds_base_name
      } else {
        file_id <- as.integer(Sys.time())  # 使用时间戳作为临时ID
      }
    }
    file_info$file_id <- file_id
    
    # 提取MS1数据
    ms1_data <- NULL
    if (length(ms1_indices) > 0) {
      ms1_data <- extract_ms1_data(spectra_obj, ms1_indices, file_id)
    }
    
    # 提取MS2数据
    ms2_data <- NULL
    if (length(ms2_indices) > 0) {
      ms2_data <- extract_ms2_data(spectra_obj, ms2_indices, file_id)
    }
    
    return(list(
      file_info = file_info,
      ms1_data = ms1_data,
      ms2_data = ms2_data
    ))
    
  }, error = function(e) {
    cat("提取数据失败:", e$message, "\n")
    return(NULL)
  })
}

# 提取MS1数据
extract_ms1_data <- function(spectra_obj, ms1_indices, file_id) {
  cat("  - 提取MS1数据...\n")
  
  # 提取MS1谱图
  ms1_spectra <- spectra_obj[ms1_indices]
  
  # 获取spectraData
  spectra_data <- Spectra::spectraData(ms1_spectra)
  
  # 转换为数据框并添加file_id
  ms1_spectra_df <- data.frame(
    file_id = file_id,
    scan_index = as.integer(spectra_data$scanIndex),
    ms_level = as.integer(spectra_data$msLevel),
    rtime = as.numeric(spectra_data$rtime),
    acquisition_num = as.integer(spectra_data$acquisitionNum),
    polarity = as.integer(spectra_data$polarity),
    peaks_count = as.integer(spectra_data$peaksCount),
    tot_ion_current = as.numeric(spectra_data$totIonCurrent),
    base_peak_mz = as.numeric(spectra_data$basePeakMZ),
    base_peak_intensity = as.numeric(spectra_data$basePeakIntensity),
    low_mz = as.numeric(spectra_data$lowMZ),
    high_mz = as.numeric(spectra_data$highMZ),
    injection_time = as.numeric(spectra_data$injectionTime),
    scan_window_lower_limit = as.numeric(spectra_data$scanWindowLowerLimit),
    scan_window_upper_limit = as.numeric(spectra_data$scanWindowUpperLimit),
    centroided = as.integer(spectra_data$centroided),
    smoothed = as.integer(spectra_data$smoothed),
    spectrum_id_original = as.character(spectra_data$spectrumId),
    filter_string = as.character(spectra_data$filterString),
    stringsAsFactors = FALSE
  )
  
  # 处理数值类型的NA值，确保数据类型一致性
  numeric_cols <- c("rtime", "tot_ion_current", "base_peak_mz", "base_peak_intensity",
                   "low_mz", "high_mz", "injection_time", "scan_window_lower_limit",
                   "scan_window_upper_limit")
  for (col in numeric_cols) {
    if (col %in% names(ms1_spectra_df)) {
      ms1_spectra_df[[col]][is.na(ms1_spectra_df[[col]])] <- NA_real_
    }
  }
  
  # 提取peaksData
  peaks_data_list <- Spectra::peaksData(ms1_spectra)
  
  # 转换peaks数据为数据框
  ms1_peaks_df <- data.frame(
    spectrum_id = integer(),
    mz = numeric(),
    intensity = numeric(),
    stringsAsFactors = FALSE
  )
  
  for (i in seq_along(peaks_data_list)) {
    peaks <- peaks_data_list[[i]]
    if (nrow(peaks) > 0) {
      peak_df <- data.frame(
        spectrum_id = i,  # 临时使用索引，后续需要更新为实际的spectrum_id
        mz = as.numeric(peaks[, "mz"]),
        intensity = as.numeric(peaks[, "intensity"]),
        stringsAsFactors = FALSE
      )
      ms1_peaks_df <- rbind(ms1_peaks_df, peak_df)
    }
  }
  
  cat("    - MS1 spectraData行数:", nrow(ms1_spectra_df), "\n")
  cat("    - MS1 peaksData行数:", nrow(ms1_peaks_df), "\n")
  
  return(list(
    spectra_data = ms1_spectra_df,
    peaks_data = ms1_peaks_df
  ))
}

# 提取MS2数据
extract_ms2_data <- function(spectra_obj, ms2_indices, file_id) {
  cat("  - 提取MS2数据...\n")
  
  # 提取MS2谱图
  ms2_spectra <- spectra_obj[ms2_indices]
  
  # 获取spectraData
  spectra_data <- Spectra::spectraData(ms2_spectra)
  
  # 转换为数据框并添加file_id
  ms2_spectra_df <- data.frame(
    file_id = file_id,
    scan_index = as.integer(spectra_data$scanIndex),
    ms_level = as.integer(spectra_data$msLevel),
    rtime = as.numeric(spectra_data$rtime),
    acquisition_num = as.integer(spectra_data$acquisitionNum),
    polarity = as.integer(spectra_data$polarity),
    prec_scan_num = as.integer(spectra_data$precScanNum),
    precursor_mz = as.numeric(spectra_data$precursorMz),
    precursor_intensity = as.numeric(spectra_data$precursorIntensity),
    precursor_charge = as.integer(spectra_data$precursorCharge),
    collision_energy = as.numeric(spectra_data$collisionEnergy),
    isolation_window_lower_mz = as.numeric(spectra_data$isolationWindowLowerMz),
    isolation_window_target_mz = as.numeric(spectra_data$isolationWindowTargetMz),
    isolation_window_upper_mz = as.numeric(spectra_data$isolationWindowUpperMz),
    peaks_count = as.integer(spectra_data$peaksCount),
    tot_ion_current = as.numeric(spectra_data$totIonCurrent),
    base_peak_mz = as.numeric(spectra_data$basePeakMZ),
    base_peak_intensity = as.numeric(spectra_data$basePeakIntensity),
    low_mz = as.numeric(spectra_data$lowMZ),
    high_mz = as.numeric(spectra_data$highMZ),
    injection_time = as.numeric(spectra_data$injectionTime),
    scan_window_lower_limit = as.numeric(spectra_data$scanWindowLowerLimit),
    scan_window_upper_limit = as.numeric(spectra_data$scanWindowUpperLimit),
    centroided = as.integer(spectra_data$centroided),
    smoothed = as.integer(spectra_data$smoothed),
    spectrum_id_original = as.character(spectra_data$spectrumId),
    filter_string = as.character(spectra_data$filterString),
    stringsAsFactors = FALSE
  )
  
  # 处理数值类型的NA值，确保数据类型一致性
  numeric_cols <- c("rtime", "precursor_mz", "precursor_intensity", "collision_energy",
                   "isolation_window_lower_mz", "isolation_window_target_mz",
                   "isolation_window_upper_mz", "tot_ion_current", "base_peak_mz",
                   "base_peak_intensity", "low_mz", "high_mz", "injection_time",
                   "scan_window_lower_limit", "scan_window_upper_limit")
  for (col in numeric_cols) {
    if (col %in% names(ms2_spectra_df)) {
      ms2_spectra_df[[col]][is.na(ms2_spectra_df[[col]])] <- NA_real_
    }
  }
  
  # 提取peaksData
  peaks_data_list <- Spectra::peaksData(ms2_spectra)
  
  # 转换peaks数据为数据框
  ms2_peaks_df <- data.frame(
    spectrum_id = integer(),
    mz = numeric(),
    intensity = numeric(),
    stringsAsFactors = FALSE
  )
  
  for (i in seq_along(peaks_data_list)) {
    peaks <- peaks_data_list[[i]]
    if (nrow(peaks) > 0) {
      peak_df <- data.frame(
        spectrum_id = i,  # 临时使用索引，后续需要更新为实际的spectrum_id
        mz = as.numeric(peaks[, "mz"]),
        intensity = as.numeric(peaks[, "intensity"]),
        stringsAsFactors = FALSE
      )
      ms2_peaks_df <- rbind(ms2_peaks_df, peak_df)
    }
  }
  
  cat("    - MS2 spectraData行数:", nrow(ms2_spectra_df), "\n")
  cat("    - MS2 peaksData行数:", nrow(ms2_peaks_df), "\n")
  
  return(list(
    spectra_data = ms2_spectra_df,
    peaks_data = ms2_peaks_df
  ))
}

# 辅助函数：安全转换数据类型
safe_convert <- function(x, target_type) {
  tryCatch({
    switch(target_type,
      "integer" = as.integer(x),
      "numeric" = as.numeric(x),
      "character" = as.character(x),
      x
    )
  }, error = function(e) {
    return(NA)
  })
}

# 批量提取目录中所有_spectra.rds文件的数据
extract_all_spectra_data <- function(cache_dir, output_dir = NULL) {
  if (!dir.exists(cache_dir)) {
    stop("缓存目录不存在:", cache_dir)
  }
  
  # 查找所有_spectra.rds文件
  rds_files <- list.files(cache_dir, pattern = "_spectra\\.rds$", full.names = TRUE)
  
  if (length(rds_files) == 0) {
    cat("在目录中未找到_spectra.rds文件:", cache_dir, "\n")
    return(NULL)
  }
  
  cat("找到", length(rds_files), "个_spectra.rds文件\n")
  cat("开始批量提取数据...\n\n")
  
  all_extracted_data <- list()
  
  for (i in seq_along(rds_files)) {
    rds_file <- rds_files[i]
    file_id <- i  # 使用序号作为file_id
    
    extracted_data <- extract_spectra_data(rds_file, file_id)
    if (!is.null(extracted_data)) {
      all_extracted_data[[basename(rds_file)]] <- extracted_data
    }
    cat("\n")
  }
  
  # 如果指定了输出目录，保存提取的数据
  if (!is.null(output_dir)) {
    if (!dir.exists(output_dir)) {
      dir.create(output_dir, recursive = TRUE)
    }
    
    output_file <- file.path(output_dir, "extracted_spectra_data.rds")
    saveRDS(all_extracted_data, output_file)
    cat("提取的数据已保存到:", output_file, "\n")
  }
  
  return(all_extracted_data)
}
