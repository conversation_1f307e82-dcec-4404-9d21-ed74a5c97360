# 数据管理服务器逻辑
# 基于路径引用的数据管理系统，不复制数据文件

# 检查是否在正确的环境中加载
if (exists("reactiveVal", where = "package:shiny") || exists("reactiveVal")) {
  # 只有在Shiny环境中才执行后续代码
  cat("[DEBUG] data_management_server.R: 在Shiny环境中，开始加载\n")

# 数据索引管理器已在global.R中加载，无需重复加载

# 响应式变量（data_loaded已在main_server.R中定义）
import_progress <- reactiveVal("")

# 辅助函数：格式化文件大小
format_file_size <- function(bytes) {
  if (is.na(bytes) || bytes == 0) return("0 B")

  units <- c("B", "KB", "MB", "GB", "TB")
  unit_index <- 1
  size <- bytes

  while (size >= 1024 && unit_index < length(units)) {
    size <- size / 1024
    unit_index <- unit_index + 1
  }

  if (unit_index == 1) {
    return(paste(size, units[unit_index]))
  } else {
    return(paste(round(size, 2), units[unit_index]))
  }
}

# 辅助函数：智能匹配列名
detect_column_match <- function(available_columns, target_patterns) {
  for (pattern in target_patterns) {
    # 精确匹配
    if (pattern %in% available_columns) {
      return(pattern)
    }

    # 忽略大小写匹配
    match_idx <- which(tolower(available_columns) == tolower(pattern))
    if (length(match_idx) > 0) {
      return(available_columns[match_idx[1]])
    }

    # 部分匹配
    match_idx <- grep(pattern, available_columns, ignore.case = TRUE)
    if (length(match_idx) > 0) {
      return(available_columns[match_idx[1]])
    }
  }

  return("")  # 没有找到匹配
}

# 检查文件夹路径
observeEvent(input$check_folder_path, {
  req(input$folder_import_path)

  path <- input$folder_import_path
  log_info(paste("检查文件夹路径:", path))

  # 只检查文件夹
  if (dir.exists(path)) {
    # 是文件夹
    tryCatch({
      # 扫描文件
      pattern <- switch(input$folder_file_type,
                       "raw" = "\\.raw$",
                       "mzML" = "\\.mzML$",
                       "mzXML" = "\\.mzXML$",
                       "mgf" = "\\.mgf$",
                       "\\.(raw|mzML|mzXML|mgf|d)$")

      files <- list.files(path, pattern = pattern, ignore.case = TRUE,
                         recursive = input$folder_include_subfolders, full.names = TRUE)

      if (length(files) == 0) {
        showEnhancedNotification("warning", "未找到文件",
                                paste("在指定文件夹中未找到", input$folder_file_type, "格式的文件"),
                                duration = 3000)
      } else {
        showEnhancedNotification("message", "文件夹检查完成",
                                paste("找到", length(files), "个文件，可以开始导入"),
                                duration = 3000)
      }
    }, error = function(e) {
      showEnhancedNotification("error", "文件夹扫描失败", e$message, duration = 3000)
    })
  } else {
    showEnhancedNotification("error", "路径无效", "指定的路径不存在", duration = 3000)
  }
})



# 数据统计输出
output$total_files_count <- renderText({
  tryCatch({
    index <- load_data_index()
    return(as.character(index$statistics$total_files))
  }, error = function(e) return("0"))
})

output$processed_files_count <- renderText({
  tryCatch({
    index <- load_data_index()
    return(as.character(index$statistics$by_status$已处理))
  }, error = function(e) return("0"))
})

output$processing_files_count <- renderText({
  tryCatch({
    index <- load_data_index()
    return(as.character(index$statistics$by_status$处理中))
  }, error = function(e) return("0"))
})

output$total_files_size <- renderText({
  tryCatch({
    index <- load_data_index()
    # 计算总大小（从索引中的文件记录）
    total_bytes <- 0
    for (file_record in index$data_files) {
      if (file.exists(file_record$file_path)) {
        file_info <- file.info(file_record$file_path)
        total_bytes <- total_bytes + file_info$size
      }
    }
    return(format_file_size(total_bytes))
  }, error = function(e) return("0 MB"))
})

# 开始文件夹导入
observeEvent(input$start_folder_import, {
  req(input$folder_import_path)

  log_info("开始文件夹导入")

  path <- input$folder_import_path

  if (!dir.exists(path)) {
    showEnhancedNotification("error", "路径无效", "指定的文件夹不存在", duration = 3000)
    return()
  }

  # 显示导入状态
  session$sendCustomMessage("showImportStatus", list(message = "正在扫描文件夹..."))

  safeExecute({
    # 文件夹导入
    added_files <- add_folder_to_index(
      folder_path = path,
      file_pattern = input$folder_file_type,
      include_subfolders = input$folder_include_subfolders,
      default_sample_type = input$folder_sample_type %||% "QC"
    )

    if (length(added_files) == 0) {
      showEnhancedNotification("warning", "未找到文件",
                              "在指定文件夹中未找到支持的数据文件",
                              duration = 5000)
      session$sendCustomMessage("hideImportStatus", list())
      return()
    }

    # 触发数据刷新
    data_loaded(!data_loaded())
    
    # 触发统计信息刷新（用于工作区统计）
    if (exists("statistics_trigger")) {
      current_value <- statistics_trigger()
      statistics_trigger(current_value + 1)
    }

    showEnhancedNotification("success", "文件夹导入完成",
                            paste("成功添加", length(added_files), "个文件到索引"), duration = 5000)
    log_info(paste("文件夹数据导入成功:", path, "- 添加", length(added_files), "个文件"))

    session$sendCustomMessage("hideImportStatus", list())
  }, error_title = "文件夹导入失败")
})

# CSV预览状态
csv_preview_data <- reactiveVal(NULL)

# 预览CSV文件
observeEvent(input$preview_csv_file, {
  req(input$csv_import_file)

  csv_file <- input$csv_import_file$datapath
  log_info("预览CSV文件")

  if (!file.exists(csv_file)) {
    showEnhancedNotification("error", "文件无效", "选择的CSV文件不存在", duration = 3000)
    csv_preview_data(NULL)
    return()
  }

  tryCatch({
    # 先读取前几行检查格式
    first_lines <- readLines(csv_file, n = 3, encoding = "UTF-8")

    # 检查是否有元数据行（如 Bracket Type=4）
    skip_rows <- 0
    if (length(first_lines) > 0) {
      first_line <- first_lines[1]
      # 如果第一行包含"="且以"Bracket Type"开头，则跳过
      if (grepl("^Bracket Type=", first_line, ignore.case = TRUE)) {
        skip_rows <- 1
      }
    }

    # 读取CSV文件前几行进行预览
    csv_data <- read.csv(csv_file, stringsAsFactors = FALSE, fileEncoding = "UTF-8",
                        nrows = 5, skip = skip_rows, header = TRUE)

    # 标准化列名
    names(csv_data) <- trimws(names(csv_data))

    # 保存预览数据
    csv_preview_data(list(
      data = csv_data,
      file_path = csv_file,
      skip_rows = skip_rows,
      columns = names(csv_data)
    ))

    # 更新列选择器
    column_choices <- c("(不选择)" = "", names(csv_data))

    # 智能匹配列名
    filename_match <- detect_column_match(names(csv_data), c("File.Name", "File Name", "文件名", "filename", "name"))
    filepath_match <- detect_column_match(names(csv_data), c("Path", "路径", "filepath", "file_path"))
    sampletype_match <- detect_column_match(names(csv_data), c("Sample.Type", "Sample Type", "样本类型", "sampletype", "type"))
    notes_match <- detect_column_match(names(csv_data), c("Comment", "备注", "notes", "remark"))

    updateSelectInput(session, "csv_filename_column", choices = column_choices, selected = filename_match)
    updateSelectInput(session, "csv_filepath_column", choices = column_choices, selected = filepath_match)
    updateSelectInput(session, "csv_sampletype_column", choices = column_choices, selected = sampletype_match)
    updateSelectInput(session, "csv_notes_column", choices = column_choices, selected = notes_match)

    showEnhancedNotification("success", "CSV预览成功",
                            paste("文件包含", ncol(csv_data), "列，请配置列映射关系"),
                            duration = 3000)

  }, error = function(e) {
    showEnhancedNotification("error", "CSV读取失败", e$message, duration = 5000)
    csv_preview_data(NULL)
  })
})

# 控制列映射配置的显示
output$csv_preview_available <- reactive({
  !is.null(csv_preview_data())
})
outputOptions(output, "csv_preview_available", suspendWhenHidden = FALSE)

# 开始CSV导入
observeEvent(input$start_csv_import, {
  req(input$csv_import_file)

  log_info("开始CSV导入")

  # 检查是否已预览文件
  preview_data <- csv_preview_data()
  if (is.null(preview_data)) {
    showEnhancedNotification("warning", "请先预览文件", "请先点击'预览文件'按钮配置列映射", duration = 3000)
    return()
  }

  # 检查必需的列映射
  if (is.null(input$csv_filename_column) || input$csv_filename_column == "") {
    showEnhancedNotification("error", "列映射不完整", "请选择文件名列", duration = 3000)
    return()
  }

  if (is.null(input$csv_filepath_column) || input$csv_filepath_column == "") {
    showEnhancedNotification("error", "列映射不完整", "请选择文件路径列", duration = 3000)
    return()
  }

  # 显示导入状态
  session$sendCustomMessage("showImportStatus", list(message = "正在处理CSV文件..."))

  safeExecute({
    # 使用列映射导入CSV
    column_mapping <- list(
      filename = input$csv_filename_column,
      filepath = input$csv_filepath_column,
      sampletype = if (input$csv_sampletype_column != "") input$csv_sampletype_column else NULL,
      notes = if (input$csv_notes_column != "") input$csv_notes_column else NULL
    )

    added_files <- import_from_csv_with_mapping(preview_data$file_path, column_mapping, preview_data$skip_rows)

    if (length(added_files) > 0) {
      data_loaded(!data_loaded())
      showEnhancedNotification("success", "CSV导入完成",
                              paste("成功添加", length(added_files), "个文件到索引"), duration = 5000)
      log_info(paste("CSV文件导入成功:", input$csv_import_file$name, "- 添加", length(added_files), "个文件"))
      
      # 触发统计信息刷新（用于工作区统计）
      if (exists("statistics_trigger")) {
        current_value <- statistics_trigger()
        statistics_trigger(current_value + 1)
      }
    } else {
      showEnhancedNotification("warning", "CSV导入完成", "未添加任何新文件", duration = 5000)
    }

    session$sendCustomMessage("hideImportStatus", list())
  }, error_title = "CSV文件导入失败")
})



# 数据表格显示（带筛选功能）
output$data_list_table <- DT::renderDataTable({
  # 添加依赖，确保数据加载后刷新
  data_loaded()
  
  tryCatch({
    # 检查是否有活动项目
    project_root <- get_project_root_path()
    if (is.null(project_root)) {
      return(data.frame(
        文件名 = "无活动项目",
        路径 = "请先创建或导入项目",
        大小 = "",
        状态 = "",
        类型 = "",
        样本类型 = "",
        修改时间 = "",
        备注 = "",
        stringsAsFactors = FALSE
      ))
    }
    
    # 获取数据索引（使用新的索引系统）
    data_list <- get_data_index_dataframe()

    if (is.null(data_list) || nrow(data_list) == 0) {
      return(data.frame(
        文件名 = character(),
        路径 = character(),
        大小 = character(),
        状态 = character(),
        类型 = character(),
        样本类型 = character(),
        修改时间 = character(),
        备注 = character(),
        stringsAsFactors = FALSE
      ))
    }
    
    # 应用样本类型筛选
    if (!is.null(input$sample_type_filter) && input$sample_type_filter != "all") {
      data_list <- data_list[data_list$样本类型 == input$sample_type_filter, ]
    }
    
    return(data_list)
  }, error = function(e) {
    log_error(paste("数据列表加载失败:", e$message))
    return(data.frame(
      文件名 = "加载失败",
      路径 = "",
      大小 = "",
      状态 = "错误",
      类型 = "",
      样本类型 = "",
      修改时间 = "",
      备注 = paste("错误:", e$message),
      stringsAsFactors = FALSE
    ))
  })
}, options = list(
  pageLength = 15,
  searching = TRUE,
  ordering = TRUE,
  info = TRUE,
  scrollX = TRUE,
  stateSave = TRUE,  # 保存表格状态
  columnDefs = list(
    list(targets = c(0, 1), searchable = TRUE),  # 文件名和路径可搜索
    list(targets = c(3, 4, 5), searchable = TRUE),  # 状态、类型、样本类型可搜索
    list(className = "dt-center", targets = c(2, 3, 4, 5, 6))  # 居中对齐
  ),
  language = list(
    search = "搜索:",
    lengthMenu = "显示 _MENU_ 条记录",
    info = "显示第 _START_ 至 _END_ 项结果，共 _TOTAL_ 项",
    paginate = list(previous = "上一页", `next` = "下一页")
  )
), selection = 'multiple')

# 注意：旧的确认导入逻辑已被新的直接处理方式替代





# 编辑选中数据
observeEvent(input$edit_selected_data, {
  selected_rows <- input$data_list_table_rows_selected

  if (is.null(selected_rows) || length(selected_rows) == 0) {
    showEnhancedNotification("warning", "未选择数据", "请先选择要编辑的数据行", duration = 3000)
    return()
  }

  if (length(selected_rows) > 1) {
    showEnhancedNotification("warning", "选择过多", "一次只能编辑一条数据记录", duration = 3000)
    return()
  }

  # 获取选中的数据（使用新的索引系统）
  data_list <- get_data_index_dataframe()
  if (is.null(data_list) || nrow(data_list) == 0) {
    showEnhancedNotification("warning", "无数据可编辑", "当前没有数据记录", duration = 3000)
    return()
  }

  selected_data <- data_list[selected_rows, ]

  # 显示编辑模态框
  showModal(modalDialog(
    title = "编辑数据记录",
    size = "l",
    div(
      h4("编辑文件信息"),
      textInput("edit_file_name", "文件名称:",
               value = selected_data$文件名, width = "100%"),

      textInput("edit_file_path", "文件路径:",
               value = selected_data$路径, width = "100%"),

      selectInput("edit_sample_type", "样本类型:",
                 choices = list("QC样本" = "QC", "标准品" = "STD",
                              "空白样本" = "BLANK", "实际样本" = "SAMPLE"),
                 selected = selected_data$样本类型),

      selectInput("edit_scan_mode", "扫描模式:",
                 choices = list("正离子模式" = "positive",
                              "负离子模式" = "negative",
                              "未知" = "Unknown"),
                 selected = ifelse(is.null(selected_data$扫描模式), "Unknown", selected_data$扫描模式)),

      selectInput("edit_status", "处理状态:",
                 choices = list("未处理" = "未处理", "处理中" = "处理中",
                              "已处理" = "已处理", "错误" = "错误"),
                 selected = selected_data$状态),

      textAreaInput("edit_notes", "备注:",
                   value = selected_data$备注, rows = 3),

      # 隐藏字段存储行号
      tags$input(id = "edit_row_index", type = "hidden", value = selected_rows)
    ),
    footer = tagList(
      modalButton("取消"),
      actionButton("confirm_edit_data", "保存修改", class = "btn-primary")
    )
  ))
})

# 确认编辑数据
observeEvent(input$confirm_edit_data, {
  if (!validateInput(input$edit_file_name, "文件名称", required = TRUE, min_length = 1)) return()
  if (!validateInput(input$edit_file_path, "文件路径", required = TRUE, min_length = 1)) return()

  # 关闭模态框
  removeModal()

  safeExecute({
    row_index <- as.numeric(input$edit_row_index)

    # 获取文件ID（使用新的索引系统）
    file_id <- get_file_id_by_row_index(row_index)

    if (is.null(file_id)) {
      showEnhancedNotification("error", "编辑失败", "数据记录不存在", duration = 3000)
      return()
    }

    # 准备更新数据
    updates <- list(
      file_name = input$edit_file_name,
      file_path = input$edit_file_path,
      sample_type = input$edit_sample_type,
      scan_mode = input$edit_scan_mode,
      status = input$edit_status,
      notes = input$edit_notes %||% ""
    )

    # 更新文件记录
    update_file_in_index(file_id, updates)

    # 触发数据加载状态更新
    data_loaded(!data_loaded())
    
    # 触发统计信息刷新（用于工作区统计）
    if (exists("statistics_trigger")) {
      current_value <- statistics_trigger()
      statistics_trigger(current_value + 1)
    }

    showEnhancedNotification("success", "编辑成功",
                            paste("已更新文件:", input$edit_file_name), duration = 3000)

  }, error_title = "编辑数据失败")
})

# 删除选中数据
observeEvent(input$remove_selected_data, {
  selected_rows <- input$data_list_table_rows_selected

  if (is.null(selected_rows) || length(selected_rows) == 0) {
    showEnhancedNotification("warning", "未选择数据", "请先选择要删除的数据行", duration = 3000)
    return()
  }

  # 显示确认对话框
  showModal(modalDialog(
    title = "确认删除",
    div(
      h4("删除确认"),
      p(paste("您确定要删除选中的", length(selected_rows), "条数据记录吗？")),
      p("此操作不可撤销。", style = "color: #dc3545; font-weight: bold;")
    ),
    footer = tagList(
      modalButton("取消"),
      actionButton("confirm_delete_data", "确认删除", class = "btn-danger")
    )
  ))
})

# 确认删除数据
observeEvent(input$confirm_delete_data, {
  selected_rows <- input$data_list_table_rows_selected

  # 关闭模态框
  removeModal()

  safeExecute({
    # 获取要删除的文件ID（使用新的索引系统）
    file_ids <- get_file_ids_by_row_indices(selected_rows)

    if (length(file_ids) == 0) {
      showEnhancedNotification("warning", "无数据可删除", "当前没有数据记录", duration = 3000)
      return()
    }

    # 批量删除文件
    removed_count <- remove_files_from_index(file_ids)

    # 触发数据加载状态更新
    data_loaded(!data_loaded())
    
    # 触发统计信息刷新（用于工作区统计）
    if (exists("statistics_trigger")) {
      current_value <- statistics_trigger()
      statistics_trigger(current_value + 1)
    }

    showEnhancedNotification("success", "删除成功",
                            paste("已删除", removed_count, "条数据记录"), duration = 3000)

  }, error_title = "删除数据失败")
})

# 导出数据列表
output$export_data_list <- downloadHandler(
  filename = function() {
    paste0("data_list_", format(Sys.time(), "%Y%m%d_%H%M%S"), ".csv")
  },
  content = function(file) {
    tryCatch({
      data_list <- get_data_index_dataframe()

      if (is.null(data_list) || nrow(data_list) == 0) {
        # 创建空的数据框
        data_list <- data.frame(
          文件名 = character(),
          路径 = character(),
          大小 = character(),
          状态 = character(),
          类型 = character(),
          样本类型 = character(),
          修改时间 = character(),
          备注 = character(),
          stringsAsFactors = FALSE
        )
      }

      # 应用当前的筛选条件
      if (!is.null(input$sample_type_filter) && input$sample_type_filter != "all") {
        data_list <- data_list[data_list$样本类型 == input$sample_type_filter, ]
      }

      write.csv(data_list, file, row.names = FALSE, fileEncoding = "UTF-8")

    }, error = function(e) {
      stop(paste("导出失败:", e$message))
    })
  },
  contentType = "text/csv"
)

# 处理Delete键事件
observeEvent(input$delete_key, {
  # 检查当前激活的标签页
  active_tab <- input$main_tabs
  
  # 只有在数据管理标签页才响应Delete键
  if (active_tab == "data_management") {
    selected_rows <- input$data_list_table_rows_selected
    
    if (!is.null(selected_rows) && length(selected_rows) > 0) {
      # 直接触发删除确认对话框
      showModal(modalDialog(
        title = "确认删除",
        div(
          h4("删除确认"),
          p(paste("您确定要删除选中的", length(selected_rows), "条数据记录吗？")),
          p("此操作不可撤销。", style = "color: #dc3545; font-weight: bold;")
        ),
        footer = tagList(
          modalButton("取消"),
          actionButton("confirm_delete_data", "确认删除", class = "btn-danger")
        )
      ))
    } else {
      showEnhancedNotification("warning", "未选择数据", "请先选择要删除的数据行", duration = 2000)
    }
  }
})

# 刷新数据列表
observeEvent(input$refresh_data_list, {
  data_loaded(!data_loaded())
  showEnhancedNotification("info", "列表已刷新", "数据列表已刷新", duration = 2000)
})

# 注意：旧的处理函数已被新的数据索引系统替代
# 现在使用 add_folder_to_index() 和 import_from_csv() 函数

} else {
  # 不在Shiny环境中，跳过所有代码
  cat("[DEBUG] data_management_server.R: 不在Shiny环境中，跳过加载\n")
}
