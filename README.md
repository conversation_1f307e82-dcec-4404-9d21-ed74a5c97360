# 实验室实时质控系统

## 项目简介

实验室实时质控系统是一个专门为LC-MS/MS DDA（Data-Dependent Acquisition）数据质量控制设计的实时监控平台。该系统基于R Shiny框架开发，提供了完整的数据导入、转换、监控和分析功能，帮助实验室实现对质谱数据的实时质量控制。

### 主要功能

- **多格式数据支持**：支持.raw、.mzML、.mzXML、.mgf等多种质谱数据格式
- **实时数据转换**：集成ProteoWizard msconvert工具，自动转换原始数据文件
- **多维度质控监控**：
  - TIC（总离子流）监控
  - BPC（基峰色谱图）监控
  - EIC（提取离子色谱图）监控
  - m/z精度监控
  - RSD（相对标准偏差）监控
- **项目管理**：支持多项目管理，每个项目独立的配置和数据存储
- **监控离子配置**：灵活的监控离子管理，支持YAML格式配置
- **实时可视化**：基于plotly的交互式图表展示
- **自动化处理**：支持文件稳定性检测和自动转换
- **报告生成**：自动生成质控报告和统计分析

### 技术特点

- **模块化架构**：采用分层设计，UI、Server、Utils、Modules各司其职
- **响应式编程**：基于Shiny的响应式编程模型，实时更新数据和界面
- **缓存机制**：智能缓存系统，提高数据处理性能
- **错误处理**：完善的错误处理和日志记录系统
- **可扩展性**：模块化设计便于功能扩展和维护

## 系统要求

### 硬件要求

- **内存**：建议8GB以上（处理大型质谱文件时建议16GB以上）
- **存储空间**：至少10GB可用空间（根据数据量调整）
- **处理器**：Intel i5或同等性能以上

### 软件要求

- **操作系统**：Windows 10/11（64位）
- **R语言**：R 4.0.0或更高版本
- **ProteoWizard**：用于数据格式转换（已包含在项目中）

### R包依赖

#### 必需的CRAN包
```r
# 核心Shiny包
shiny (>= 1.7.0)
DT (>= 0.18)
plotly (>= 4.10.0)
dplyr (>= 1.0.0)
shinyjs (>= 2.1.0)
shinyFiles (>= 0.9.0)

# 数据处理包
yaml (>= 2.3.0)
jsonlite (>= 1.8.0)
fs (>= 1.5.0)
later (>= 1.3.0)

# 其他工具包
tools
utils
```

#### 必需的Bioconductor包
```r
# 质谱数据处理
Spectra (>= 1.8.0)
MsBackendMzR (>= 1.6.0)
MsBackendRawFileReader (>= 1.4.0)  # 可选，用于.raw文件支持
```

## 安装指南

### 1. 环境准备

#### 安装R语言
1. 从[R官网](https://www.r-project.org/)下载并安装R 4.0.0或更高版本
2. 建议同时安装[RStudio](https://www.rstudio.com/)作为开发环境

#### 安装必需的R包
```r
# 安装CRAN包
install.packages(c(
  "shiny", "DT", "plotly", "dplyr", "shinyjs", "shinyFiles",
  "yaml", "jsonlite", "fs", "later"
))

# 安装Bioconductor包
if (!requireNamespace("BiocManager", quietly = TRUE))
    install.packages("BiocManager")

BiocManager::install(c(
  "Spectra",
  "MsBackendMzR",
  "MsBackendRawFileReader"
))
```

### 2. 系统部署

#### 下载项目
```bash
# 克隆或下载项目到本地目录
git clone [项目地址]
# 或直接下载压缩包并解压
```

#### 配置环境
1. 确保项目目录中包含完整的`ProteoWizard`文件夹
2. 检查`R`文件夹是否包含所需的R运行环境
3. 验证所有依赖包是否正确安装

### 3. 启动系统

#### 方法一：直接运行
```r
# 在R控制台中
setwd("path/to/project")  # 设置工作目录
source("app.R")           # 启动应用
```

#### 方法二：RStudio运行
1. 在RStudio中打开项目文件夹
2. 打开`app.R`文件
3. 点击"Run App"按钮

#### 方法三：命令行运行
```bash
cd path/to/project
Rscript app.R
```

### 4. 访问系统

系统启动后，在浏览器中访问：
```
http://127.0.0.1:3839
```

## 快速开始

### 1. 创建项目

1. 启动系统后，在项目选择页面点击"创建新项目"
2. 输入项目名称和描述
3. 选择项目存储路径（可选，默认在系统目录下）
4. 点击"创建项目"

### 2. 导入数据

1. 进入工作区后，切换到"数据管理"标签页
2. 选择数据导入方式：
   - **文件导入**：选择单个或多个数据文件
   - **文件夹导入**：批量导入整个文件夹的数据
   - **CSV导入**：导入样本信息列表
3. 设置样本类型（QC、样本、空白等）
4. 点击"开始导入"

### 3. 配置监控离子

1. 切换到"监控参数"标签页
2. 在"监控离子管理"部分添加监控离子：
   - 输入化合物名称
   - 设置分子质量
   - 配置离子质荷比
   - 选择离子类型
3. 保存监控离子配置

### 4. 启动监控

1. 在"监控参数"标签页选择监控类型：
   - TIC：总离子流监控
   - BPC：基峰色谱图监控
   - EIC：提取离子色谱图监控
   - m/z：质荷比精度监控
   - RSD：相对标准偏差监控
2. 点击"开始监控"
3. 切换到"监控图表"标签页查看实时监控结果

### 5. 查看结果

1. **实时图表**：在"监控图表"标签页查看各种监控图表
2. **统计信息**：在"项目信息"标签页查看项目统计
3. **导出报告**：点击"导出报告"生成质控报告

## 详细使用说明

### 项目管理

#### 项目结构
每个项目包含以下目录结构：
```
项目名称/
├── data/           # 原始数据文件
│   ├── cache/      # 缓存文件
│   └── mzML/       # 转换后的mzML文件
├── results/        # 监控结果数据
├── reports/        # 生成的报告
└── config/         # 项目配置文件
    ├── project.json        # 项目基本信息
    └── monitor_ions.yaml   # 监控离子配置
```

#### 项目操作
- **创建项目**：支持自定义路径创建
- **导入项目**：导入已存在的项目
- **项目切换**：支持多项目并行工作
- **项目设置**：修改项目配置信息

### 数据管理

#### 支持的文件格式
- **.raw**：Thermo Fisher原始数据文件
- **.mzML**：标准质谱数据交换格式
- **.mzXML**：质谱数据XML格式
- **.mgf**：Mascot通用格式
- **.csv**：样本信息表格

#### 数据导入流程
1. **文件验证**：检查文件格式和完整性
2. **格式转换**：使用ProteoWizard转换为mzML格式
3. **数据解析**：使用Spectra包解析质谱数据
4. **缓存存储**：将解析结果缓存以提高性能
5. **索引更新**：更新数据索引和统计信息

#### 自动转换功能
- **文件稳定性检测**：监控文件大小变化，确保文件传输完成
- **自动转换触发**：检测到稳定文件后自动启动转换
- **进度跟踪**：实时显示转换进度和状态
- **错误处理**：转换失败时的错误记录和重试机制

### 监控系统

#### 监控类型详解

##### TIC（总离子流）监控
- **用途**：监控整体信号强度和稳定性
- **指标**：信号强度、变异系数、趋势分析
- **阈值**：可配置的RSD限制（默认15%）

##### BPC（基峰色谱图）监控
- **用途**：监控最强信号的稳定性
- **指标**：基峰强度、保留时间稳定性
- **阈值**：可配置的RSD限制（默认20%）

##### EIC（提取离子色谱图）监控
- **用途**：监控特定离子的色谱行为
- **指标**：峰面积、保留时间、峰形
- **配置**：需要预先配置监控离子

##### m/z精度监控
- **用途**：监控质量精度和校准状态
- **指标**：质量偏差、质量精度
- **阈值**：可配置的质量容差（默认0.01 Da）

##### RSD监控
- **用途**：监控技术重现性
- **指标**：QC样本间的相对标准偏差
- **应用**：特别适用于QC样本的质量评估

#### 监控离子配置

##### YAML配置格式
```yaml
monitor_ions:
  - compound_name: "化合物A"
    molecular_weight: 180.0634
    retention_time: 5.2
    scan_mode: "DDA"
    ionization_mode: "positive"
    monitor_ions:
      - mz: 181.0712
        ion_type: "[M+H]+"
        ms_level: 1
        notes: "分子离子峰"

global_settings:
  default_tolerance: 0.01
  retention_time_window: 0.5
  min_intensity: 1000
  max_intensity: 1000000

instrument_settings:
  mass_analyzer: "Orbitrap"
  ionization_mode: "ESI"
  scan_type: "DDA"
  ms1_resolution: 70000
  ms2_resolution: 17500
```

##### 配置管理
- **添加离子**：通过界面或导入YAML文件
- **编辑离子**：修改现有监控离子参数
- **删除离子**：移除不需要的监控离子
- **导出配置**：将配置导出为YAML文件
- **导入配置**：从YAML文件导入监控离子

### 可视化系统

#### 图表类型
- **时间序列图**：显示监控指标随时间的变化
- **散点图**：显示数据点分布和异常值
- **箱线图**：显示数据分布和统计特征
- **热图**：显示多维数据的相关性

#### 交互功能
- **缩放**：支持图表的放大和缩小
- **平移**：支持图表的拖拽移动
- **选择**：支持数据点的选择和高亮
- **导出**：支持图表的PNG、PDF导出

#### 时间范围控制
- **实时更新**：自动刷新最新数据
- **时间窗口**：1小时、6小时、24小时、全部
- **自定义范围**：用户自定义时间范围

## 配置指南

### 系统配置

#### 全局配置文件
位置：`data/config/global_config.json`
```json
{
  "app_name": "实验室实时质控系统",
  "version": "1.0.0",
  "default_settings": {
    "auto_refresh_interval": 30,
    "max_file_size": 1024,
    "cache_enabled": true
  }
}
```

#### 项目配置文件
位置：`项目目录/config/project.json`
```json
{
  "name": "项目名称",
  "path": "项目路径",
  "created_time": "2024-01-01T00:00:00",
  "description": "项目描述",
  "status": "active",
  "monitor_config": {
    "enabled_monitors": ["tic", "bpc", "eic"],
    "refresh_interval": 60
  }
}
```

### 监控配置

#### 阈值设置
```yaml
qc_thresholds:
  tic_rsd_limit: 15.0      # TIC RSD限制 (%)
  bpc_rsd_limit: 20.0      # BPC RSD限制 (%)
  mz_tolerance: 0.01       # 质量容差 (Da)
  retention_time_window: 0.5  # 保留时间窗口 (min)
  min_intensity: 1000      # 最小强度阈值
  max_intensity: 1000000   # 最大强度阈值
```

#### 仪器设置
```yaml
instrument_settings:
  mass_analyzer: "Orbitrap"     # 质量分析器类型
  ionization_mode: "ESI"        # 离子化模式
  scan_type: "DDA"              # 扫描类型
  ms1_resolution: 70000         # MS1分辨率
  ms2_resolution: 17500         # MS2分辨率
  collision_energy_mode: "HCD"  # 碰撞能量模式
```

### 性能优化

#### 缓存配置
- **启用缓存**：在配置中设置`cache_enabled: true`
- **缓存位置**：`项目目录/data/cache/`
- **缓存清理**：定期清理过期缓存文件

#### 内存管理
- **大文件处理**：分批处理大型数据文件
- **内存监控**：监控R进程内存使用情况
- **垃圾回收**：定期执行`gc()`清理内存

#### 并发处理
- **文件转换**：支持多文件并行转换
- **数据处理**：使用异步处理提高性能
- **UI响应**：避免长时间阻塞UI线程

## 故障排除

### 常见问题

#### 1. 系统启动失败

**问题描述**：运行`app.R`时出现错误

**可能原因**：
- R包依赖缺失
- R版本过低
- 工作目录设置错误

**解决方案**：
```r
# 检查R版本
R.version.string

# 检查包安装状态
installed.packages()[c("shiny", "DT", "plotly"), ]

# 重新安装缺失的包
install.packages(c("shiny", "DT", "plotly"))

# 设置正确的工作目录
setwd("path/to/project")
```

#### 2. 数据转换失败

**问题描述**：.raw文件转换为mzML失败

**可能原因**：
- ProteoWizard路径错误
- 文件权限问题
- 磁盘空间不足

**解决方案**：
```r
# 检查msconvert工具是否存在
file.exists("ProteoWizard/msconvert.exe")

# 检查磁盘空间
disk.free(".")

# 检查文件权限
file.access("path/to/file.raw", mode = 4)  # 读权限
```

#### 3. 监控数据异常

**问题描述**：监控图表显示异常数据或无数据

**可能原因**：
- 监控离子配置错误
- 数据文件损坏
- 缓存文件过期

**解决方案**：
```r
# 清理缓存
unlink("data/cache", recursive = TRUE)

# 重新加载监控离子配置
source("utils/monitor_ions_manager.R")
load_project_monitor_ions()

# 检查数据文件完整性
file.info("path/to/data.mzML")
```

#### 4. 内存不足

**问题描述**：处理大文件时出现内存不足错误

**解决方案**：
```r
# 增加内存限制
memory.limit(size = 8192)  # 8GB

# 清理内存
gc()
rm(list = ls())

# 分批处理大文件
# 在代码中实现分块读取
```

#### 5. 浏览器兼容性问题

**问题描述**：某些浏览器中界面显示异常

**解决方案**：
- 推荐使用Chrome或Firefox最新版本
- 清除浏览器缓存
- 禁用浏览器扩展
- 检查JavaScript是否启用

### 日志系统

#### 日志位置
- **系统日志**：`logs/system.log`
- **错误日志**：`logs/error.log`
- **调试日志**：`logs/debug.log`

#### 日志级别
- **ERROR**：错误信息
- **WARNING**：警告信息
- **INFO**：一般信息
- **DEBUG**：调试信息

#### 查看日志
```r
# 查看最新日志
tail("logs/system.log", n = 50)

# 搜索特定错误
grep("ERROR", readLines("logs/error.log"))
```

### 性能监控

#### 系统监控
```r
# 检查内存使用
memory.size()
memory.size(max = TRUE)

# 检查对象大小
object.size(data_object)

# 性能分析
system.time({
  # 执行代码
})
```

#### 数据库监控
```r
# 检查缓存大小
sum(file.size(list.files("data/cache", recursive = TRUE)))

# 检查文件数量
length(list.files("data", recursive = TRUE))
```

## 开发指南

### 项目架构

#### 目录结构
```
实验室实时质控/
├── app.R                 # 主应用入口
├── global.R              # 全局配置
├── ui/                   # UI模块
│   ├── main_ui.R
│   ├── project_selection_ui.R
│   └── workspace_ui.R
├── server/               # 服务器逻辑
│   ├── main_server.R
│   ├── project_selection_server.R
│   ├── workspace_server.R
│   └── data_management_server.R
├── utils/                # 工具函数
│   ├── config_manager.R
│   ├── logger.R
│   ├── error_handler.R
│   ├── path_manager.R
│   ├── project_manager.R
│   ├── data_index_manager.R
│   ├── monitor_ions_manager.R
│   ├── monitoring_controller.R
│   └── data_processor.R
├── modules/              # 功能模块
│   ├── data_adapters/
│   │   └── spectra_adapter.R
│   └── monitors/
│       └── monitor_ions_config.R
├── www/                  # 静态资源
│   ├── css/
│   └── js/
├── data/                 # 数据目录
├── logs/                 # 日志目录
├── ProteoWizard/         # 外部工具
└── R/                    # R运行环境
```

#### 模块说明

##### UI层
- **main_ui.R**：主界面布局和页面切换
- **project_selection_ui.R**：项目选择页面UI
- **workspace_ui.R**：工作区页面UI

##### Server层
- **main_server.R**：主服务器逻辑和事件处理
- **project_selection_server.R**：项目选择相关逻辑
- **workspace_server.R**：工作区功能逻辑
- **data_management_server.R**：数据管理相关逻辑

##### Utils层
- **config_manager.R**：配置文件管理
- **logger.R**：日志系统
- **error_handler.R**：错误处理
- **path_manager.R**：路径管理
- **project_manager.R**：项目管理
- **data_index_manager.R**：数据索引管理
- **monitor_ions_manager.R**：监控离子管理
- **monitoring_controller.R**：监控控制器
- **data_processor.R**：数据处理器

##### Modules层
- **data_adapters/**：数据适配器模块
- **monitors/**：监控模块

### 代码规范

#### R代码风格
```r
# 函数命名：使用下划线分隔
process_data_file <- function(file_path) {
  # 函数体
}

# 变量命名：使用下划线分隔
data_file_path <- "path/to/file"

# 常量命名：使用大写字母
DEFAULT_CONFIG <- list(...)

# 注释：使用中文注释
# 这是一个处理数据的函数
```

#### 文件组织
- 每个文件开头包含文件说明注释
- 函数按功能分组
- 导出函数在文件开头
- 内部函数在文件末尾

#### 错误处理
```r
# 使用tryCatch进行错误处理
result <- tryCatch({
  # 主要逻辑
  process_data(data)
}, error = function(e) {
  # 错误处理
  log_error(paste("处理失败:", e$message))
  return(list(success = FALSE, error = e$message))
})
```

### 扩展开发

#### 添加新的监控类型
1. 在`utils/monitoring_controller.R`中添加数据生成函数
2. 在`server/main_server.R`中添加图表渲染逻辑
3. 在`ui/workspace_ui.R`中添加UI元素
4. 更新配置文件格式

#### 添加新的数据格式支持
1. 在`modules/data_adapters/spectra_adapter.R`中添加新的backend
2. 更新`select_backend`函数
3. 添加格式验证逻辑
4. 更新支持格式列表

#### 添加新的可视化图表
1. 在`server/main_server.R`中添加新的`renderPlotly`函数
2. 在`ui/workspace_ui.R`中添加对应的`plotlyOutput`
3. 实现数据获取和处理逻辑
4. 添加图表配置选项

## 贡献指南

### 开发环境设置
1. 安装R和RStudio
2. 克隆项目代码
3. 安装所有依赖包
4. 运行测试确保环境正常

### 提交代码
1. 创建功能分支
2. 编写代码和测试
3. 更新文档
4. 提交Pull Request

### 代码审查
- 确保代码符合项目规范
- 添加必要的注释和文档
- 通过所有测试
- 性能影响评估

## 许可证

本项目采用 [MIT许可证](LICENSE)。

## 联系方式

如有问题或建议，请联系：
- 邮箱：[联系邮箱]
- 项目地址：[项目仓库地址]

## 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 基本的数据导入和转换功能
- TIC、BPC、EIC监控功能
- 项目管理系统
- 监控离子配置管理

---

*最后更新：2024年1月*