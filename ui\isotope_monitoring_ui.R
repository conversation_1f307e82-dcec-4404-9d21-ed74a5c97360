# UI层 - 同位素内标监控界面
# 提供同位素内标监控的用户界面组件

# 同位素内标监控UI模块
isotope_monitoring_ui <- function() {
  tagList(
    # 同位素内标监控面板
    div(class = "card mb-4",
      div(class = "card-header d-flex justify-content-between align-items-center collapsible-header",
        onclick = "toggleCollapse('isotope_monitoring_content')",
        h5(class = "mb-0", 
          icon("atom"), " 同位素内标监控",
          span(class = "badge badge-info ml-2", id = "isotope_status_badge", "未运行")
        ),
        span(class = "collapse-icon", id = "isotope_monitoring_icon", "▼")
      ),
      div(id = "isotope_monitoring_content", class = "card-body module-content",
        
        # 系统状态面板
        div(class = "row mb-3",
          div(class = "col-12",
            div(class = "alert alert-info", id = "isotope_system_status",
              div(class = "d-flex justify-content-between align-items-center",
                div(
                  strong("系统状态: "),
                  span(id = "isotope_processor_status", "检查中...")
                ),
                actionButton("reinit_isotope_processor",
                           "重新初始化",
                           class = "btn btn-sm btn-outline-primary",
                           icon = icon("refresh"))
              )
            )
          )
        ),

        # 控制面板
        div(class = "row mb-3",
          div(class = "col-md-6",
            h6("分析参数"),
            div(class = "form-group",
              tags$label("质量容差 (ppm):", `for` = "isotope_mass_tolerance"),
              numericInput("isotope_mass_tolerance",
                          label = NULL,
                          value = 10,
                          min = 1,
                          max = 100,
                          step = 1)
            ),
            div(class = "form-check",
              checkboxInput("isotope_include_ms2",
                           "包含MS2-MS1匹配分析",
                           value = TRUE)
            )
          ),
          div(class = "col-md-6",
            h6("样本选择"),
            div(class = "form-group",
              tags$label("选择样本文件:", `for` = "isotope_file_selection"),
              selectInput("isotope_file_selection",
                         label = NULL,
                         choices = list("所有文件" = "all"),
                         multiple = TRUE,
                         selected = "all")
            )
          )
        ),
        
        # 操作按钮
        div(class = "row mb-3",
          div(class = "col-12",
            actionButton("run_isotope_analysis", 
                        "开始同位素内标分析", 
                        class = "btn btn-primary mr-2",
                        icon = icon("play")),
            actionButton("refresh_isotope_data", 
                        "刷新数据", 
                        class = "btn btn-secondary mr-2",
                        icon = icon("refresh")),
            downloadButton("download_isotope_results", 
                          "下载结果", 
                          class = "btn btn-success",
                          icon = icon("download"))
          )
        ),
        
        # 分析状态显示
        div(class = "row mb-3",
          div(class = "col-12",
            div(id = "isotope_analysis_status", 
                class = "alert alert-info", 
                style = "display: none;",
                "分析状态将在这里显示...")
          )
        ),
        
        # 结果摘要
        div(class = "row mb-3",
          div(class = "col-md-3",
            div(class = "card bg-light",
              div(class = "card-body text-center",
                h4(id = "isotope_total_compounds", "0", class = "text-primary"),
                p("监控化合物数", class = "mb-0")
              )
            )
          ),
          div(class = "col-md-3",
            div(class = "card bg-light",
              div(class = "card-body text-center",
                h4(id = "isotope_total_samples", "0", class = "text-info"),
                p("分析样本数", class = "mb-0")
              )
            )
          ),
          div(class = "col-md-3",
            div(class = "card bg-light",
              div(class = "card-body text-center",
                h4(id = "isotope_total_points", "0", class = "text-success"),
                p("数据点总数", class = "mb-0")
              )
            )
          ),
          div(class = "col-md-3",
            div(class = "card bg-light",
              div(class = "card-body text-center",
                h4(id = "isotope_ms2_matched", "0", class = "text-warning"),
                p("MS2匹配点数", class = "mb-0")
              )
            )
          )
        ),
        
        # 标签页面板
        tabsetPanel(id = "isotope_tabs",
          
          # 3D散点图标签页
          tabPanel("3D散点图", 
            value = "scatter_plot",
            div(class = "mt-3",
              h6("同位素内标MS1三维散点图"),
              p("X轴: 保留时间, Y轴: 样本名称, Z轴: 强度, 颜色: m/z偏差, 形状: MS2存在性"),
              div(style = "height: 600px;",
                plotlyOutput("isotope_3d_scatter", height = "100%")
              )
            )
          ),
          
          # MS2谱图标签页
          tabPanel("MS2谱图", 
            value = "ms2_spectrum",
            div(class = "mt-3",
              div(class = "row mb-3",
                div(class = "col-md-6",
                  selectInput("ms2_spectrum_selection",
                             "选择MS2谱图:",
                             choices = list("请先运行分析" = ""),
                             width = "100%")
                ),
                div(class = "col-md-6",
                  actionButton("load_ms2_spectrum", 
                              "加载谱图", 
                              class = "btn btn-primary",
                              icon = icon("chart-line"))
                )
              ),
              div(style = "height: 500px;",
                plotlyOutput("isotope_ms2_spectrum", height = "100%")
              )
            )
          ),
          
          # 信息表格标签页
          tabPanel("信息表格", 
            value = "info_table",
            div(class = "mt-3",
              h6("同位素内标详细信息"),
              p("包含保留时间、m/z偏差、峰形参数、信噪比等详细信息"),
              div(class = "table-responsive",
                DT::dataTableOutput("isotope_info_table")
              )
            )
          ),
          
          # 质量控制标签页
          tabPanel("质量控制", 
            value = "quality_control",
            div(class = "mt-3",
              h6("数据质量评估"),
              div(class = "row",
                div(class = "col-md-6",
                  h6("质量问题统计"),
                  div(id = "quality_issues_summary",
                    "运行分析后显示质量问题统计..."
                  )
                ),
                div(class = "col-md-6",
                  h6("质量控制图表"),
                  div(style = "height: 400px;",
                    plotlyOutput("isotope_quality_plot", height = "100%")
                  )
                )
              )
            )
          )
        )
      )
    )
  )
}




