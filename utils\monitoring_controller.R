# Utils层 - 监控控制器
# 提供监控任务的启动、停止和报告导出功能

# 全局监控状态
MONITORING_STATUS <- FALSE
CURRENT_MONITORING_CONFIG <- NULL

# 开始监控任务
start_monitoring_tasks <- function(monitor_config) {
  tryCatch({
    log_info("开始启动监控任务")
    
    # 验证配置
    if (is.null(monitor_config$project)) {
      stop("监控配置缺少项目信息")
    }
    
    if (is.null(monitor_config$types) || length(monitor_config$types) == 0) {
      stop("监控配置缺少监控类型")
    }

    # 检查是否需要监控离子
    needs_ions <- any(c("mz", "rsd", "eic") %in% monitor_config$types)
    if (needs_ions && (is.null(monitor_config$ions) || length(monitor_config$ions) == 0)) {
      stop("选择的监控类型需要监控离子配置")
    }
    
    # 保存配置
    CURRENT_MONITORING_CONFIG <<- monitor_config
    
    # 设置监控状态
    MONITORING_STATUS <<- TRUE
    
    # 记录监控开始
    log_info(paste("监控任务已启动 - 项目:", monitor_config$project))
    log_info(paste("监控类型:", paste(monitor_config$types, collapse = ", ")))
    if (!is.null(monitor_config$ions) && length(monitor_config$ions) > 0) {
      log_info(paste("监控离子数量:", length(monitor_config$ions)))
    } else {
      log_info("监控类型不需要离子配置")
    }
    
    # 启动监控数据生成（模拟）
    start_monitoring_simulation(monitor_config)

    return(list(success = TRUE, message = "监控任务启动成功"))
    
  }, error = function(e) {
    log_error(paste("启动监控任务失败:", e$message))
    return(list(success = FALSE, message = paste("启动监控任务失败:", e$message)))
  })
}

# 停止监控任务
stop_monitoring_tasks <- function() {
  tryCatch({
    log_info("开始停止监控任务")
    
    # 设置监控状态
    MONITORING_STATUS <<- FALSE
    
    # 清理配置
    CURRENT_MONITORING_CONFIG <<- NULL
    
    # 这里可以添加实际的停止逻辑
    # 例如：停止数据采集、清理定时器等
    
    log_info("监控任务已停止")
    return(list(success = TRUE, message = "监控任务停止成功"))
    
  }, error = function(e) {
    log_error(paste("停止监控任务失败:", e$message))
    return(list(success = FALSE, message = paste("停止监控任务失败:", e$message)))
  })
}

# 获取监控状态
get_monitoring_status <- function() {
  return(MONITORING_STATUS)
}

# 获取当前监控配置
get_current_monitoring_config <- function() {
  return(CURRENT_MONITORING_CONFIG)
}

# 生成并导出报告
generate_and_export_report <- function(project_name) {
  tryCatch({
    log_info(paste("开始生成项目报告:", project_name))
    
    if (is.null(project_name) || project_name == "") {
      stop("项目名称不能为空")
    }
    
    # 获取项目信息
    project_info <- get_project_info(project_name)
    if (!is.null(project_info$error)) {
      stop(paste("获取项目信息失败:", project_info$error))
    }
    
    # 创建报告内容
    report_content <- paste(
      "# 实验室实时质控报告",
      "",
      paste("**项目名称:**", project_info$name),
      paste("**创建时间:**", project_info$created_time),
      paste("**项目描述:**", project_info$description),
      paste("**项目状态:**", project_info$status),
      "",
      "## 数据统计",
      paste("- 原始数据文件数:", project_info$raw_data_count),
      paste("- 结果文件数:", project_info$result_count),
      paste("- 报告文件数:", project_info$report_count),
      paste("- 项目总大小:", project_info$total_size_mb, "MB"),
      "",
      "## 监控配置",
      ifelse(is.null(CURRENT_MONITORING_CONFIG), 
             "当前无监控配置", 
             paste("- 监控类型:", paste(CURRENT_MONITORING_CONFIG$types, collapse = ", "))),
      "",
      "## 生成时间",
      paste("报告生成时间:", format(Sys.time(), "%Y-%m-%d %H:%M:%S")),
      "",
      "---",
      "*此报告由实验室实时质控系统自动生成*",
      sep = "\n"
    )
    
    # 保存报告文件
    report_dir <- file.path(get_project_path(project_name), "reports")
    if (!dir.exists(report_dir)) {
      dir.create(report_dir, recursive = TRUE)
    }
    
    report_file <- file.path(report_dir, paste0("report_", format(Sys.time(), "%Y%m%d_%H%M%S"), ".md"))
    writeLines(report_content, report_file, useBytes = TRUE)
    
    log_info(paste("报告已生成:", report_file))
    return(list(success = TRUE, file = report_file))
    
  }, error = function(e) {
    log_error(paste("生成报告失败:", e$message))
    return(list(success = FALSE, message = paste("生成报告失败:", e$message)))
  })
}

# 获取监控结果数据（增强版）
get_monitoring_results <- function(project_name) {
  tryCatch({
    if (is.null(project_name) || project_name == "") {
      return(data.frame(时间 = character(), 类型 = character(), 值 = numeric(), 状态 = character()))
    }

    # 从模拟数据文件中读取数据
    project_path <- file.path(getwd(), "data", "projects", project_name)
    results_dir <- file.path(project_path, "results")

    if (!dir.exists(results_dir)) {
      return(data.frame(时间 = character(), 类型 = character(), 值 = numeric(), 状态 = character()))
    }

    # 读取所有监控数据文件
    all_results <- data.frame()
    result_files <- list.files(results_dir, pattern = "*_data.csv", full.names = TRUE)

    for (file_path in result_files) {
      if (file.exists(file_path)) {
        file_data <- read.csv(file_path, stringsAsFactors = FALSE, fileEncoding = "UTF-8")

        # 添加类型信息
        file_type <- gsub("_data.csv", "", basename(file_path))
        file_data$类型 <- toupper(file_type)

        # 统一列名
        if ("mz" %in% names(file_data)) {
          file_data$类型 <- paste(file_data$类型, file_data$mz, sep = "_")
        }

        all_results <- rbind(all_results, file_data)
      }
    }

    # 如果没有数据，返回空数据框
    if (nrow(all_results) == 0) {
      return(data.frame(时间 = character(), 类型 = character(), 值 = numeric(), 状态 = character()))
    }

    return(all_results)

  }, error = function(e) {
    log_error(paste("获取监控结果失败:", e$message))
    return(data.frame(时间 = character(), 类型 = character(), 值 = numeric(), 状态 = character()))
  })
}

# 获取图表数据（增强版）
get_plot_data <- function(project_name, plot_type) {
  tryCatch({
    if (is.null(project_name) || project_name == "") {
      return(data.frame(time = character(), value = numeric()))
    }

    # 从模拟数据文件中读取数据
    project_path <- file.path(getwd(), "data", "projects", project_name)
    plot_file <- file.path(project_path, "results", paste0(plot_type, "_data.csv"))

    if (file.exists(plot_file)) {
      plot_data <- read.csv(plot_file, stringsAsFactors = FALSE, fileEncoding = "UTF-8")

      # 统一列名为time和value
      if ("时间" %in% names(plot_data) && "值" %in% names(plot_data)) {
        plot_data$time <- plot_data$时间
        plot_data$value <- plot_data$值
      }

      # 确保有time和value列
      if (!("time" %in% names(plot_data)) || !("value" %in% names(plot_data))) {
        return(data.frame(time = character(), value = numeric()))
      }

      return(plot_data[, c("time", "value")])
    } else {
      # 返回空数据框
      return(data.frame(time = character(), value = numeric()))
    }

  }, error = function(e) {
    log_error(paste("读取图表数据失败:", e$message))
    return(data.frame(time = character(), value = numeric()))
  })
}

# 获取数据列表（真实数据）
get_data_list <- function(project_name) {
  # 从项目目录中读取真实的数据文件列表
  
  if (is.null(project_name) || project_name == "") {
    return(data.frame())
  }
  
  project_path <- get_project_path(project_name)
  raw_data_dir <- file.path(project_path, "raw_data")
  
  if (!dir.exists(raw_data_dir)) {
    return(data.frame())
  }
  
  tryCatch({
    # 获取所有数据文件
    files <- list.files(raw_data_dir, recursive = TRUE, full.names = FALSE)
    
    if (length(files) == 0) {
      return(data.frame())
    }
    
    # 获取文件信息
    file_info <- data.frame(
      文件名 = files,
      大小 = sapply(file.path(raw_data_dir, files), function(f) {
        size_bytes <- file.size(f)
        if (size_bytes < 1024^2) {
          paste(round(size_bytes / 1024, 1), "KB")
        } else {
          paste(round(size_bytes / 1024^2, 1), "MB")
        }
      }),
      状态 = rep("待处理", length(files)),
      上传时间 = sapply(file.path(raw_data_dir, files), function(f) {
        format(file.info(f)$mtime, "%Y-%m-%d %H:%M:%S")
      }),
      stringsAsFactors = FALSE
    )
    
    return(file_info)
    
  }, error = function(e) {
    log_error(paste("获取数据列表失败:", e$message))
    return(data.frame())
  })
}

# 生成统计报告
generate_statistics_report <- function(project_name) {
  # 基于真实数据生成统计报告
  
  if (is.null(project_name) || project_name == "") {
    return("项目名称不能为空")
  }
  
  tryCatch({
    # 获取项目信息
    project_info <- get_project_info(project_name)
    if (!is.null(project_info$error)) {
      return(paste("获取项目信息失败:", project_info$error))
    }
    
    # 获取数据列表
    data_list <- get_data_list(project_name)
    
    # 获取监控结果
    monitoring_results <- get_monitoring_results(project_name)
    
    # 计算统计信息
    total_files <- nrow(data_list)
    processed_files <- sum(data_list$状态 == "已处理", na.rm = TRUE)
    processing_files <- sum(data_list$状态 == "处理中", na.rm = TRUE)
    pending_files <- sum(data_list$状态 == "待处理", na.rm = TRUE)
    
    monitoring_duration <- ifelse(nrow(monitoring_results) > 0, 
                                 paste(nrow(monitoring_results), "个数据点"), 
                                 "无监控数据")
    anomaly_count <- ifelse(nrow(monitoring_results) > 0,
                           sum(monitoring_results$状态 == "异常", na.rm = TRUE),
                           0)
    
    # 生成报告
    report <- paste(
      "=== 项目统计报告 ===",
      "",
      paste("项目名称:", project_info$name),
      paste("项目描述:", project_info$description),
      paste("创建时间:", project_info$created_time),
      paste("报告生成时间:", format(Sys.time(), "%Y-%m-%d %H:%M:%S")),
      "",
      "数据统计:",
      paste("- 总文件数:", total_files),
      paste("- 已处理文件:", processed_files),
      paste("- 处理中文件:", processing_files),
      paste("- 待处理文件:", pending_files),
      paste("- 项目总大小:", project_info$total_size_mb, "MB"),
      "",
      "监控统计:",
      paste("- 监控数据点:", monitoring_duration),
      paste("- 异常检测:", anomaly_count),
      paste("- 监控状态:", ifelse(get_monitoring_status(), "运行中", "已停止")),
      "",
      "性能指标:",
      paste("- 处理成功率:", ifelse(total_files > 0, 
                                   round(processed_files / total_files * 100, 1), 
                                   0), "%"),
      paste("- 系统状态:", ifelse(anomaly_count == 0, "正常", "异常")),
      "",
      "=== 报告结束 ===",
      sep = "\n"
    )
    
    return(report)
    
  }, error = function(e) {
    log_error(paste("生成统计报告失败:", e$message))
    return(paste("生成统计报告失败:", e$message))
  })
}

# 删除选中的数据
remove_selected_data <- function(project_name, selected_rows) {
  # 这里实现删除选中数据的逻辑
  # 实际应用中应该删除真实的文件
  
  log_info(paste("删除项目", project_name, "中的", length(selected_rows), "个数据文件"))
  return(list(success = TRUE, message = paste("已删除", length(selected_rows), "个数据文件")))
}

# 启动监控模拟
start_monitoring_simulation <- function(monitor_config) {
  tryCatch({
    log_info("启动监控数据模拟")

    # 创建结果目录
    project_path <- file.path(getwd(), "data", "projects", monitor_config$project)
    results_dir <- file.path(project_path, "results")
    if (!dir.exists(results_dir)) {
      dir.create(results_dir, recursive = TRUE, showWarnings = FALSE)
    }

    # 生成模拟监控数据
    generate_simulation_data(monitor_config, results_dir)

    log_info("监控数据模拟已启动")

  }, error = function(e) {
    log_error(paste("启动监控模拟失败:", e$message))
  })
}

# 生成模拟监控数据
generate_simulation_data <- function(monitor_config, results_dir) {
  tryCatch({
    # 生成时间序列数据
    current_time <- Sys.time()
    time_points <- seq(from = current_time - 3600, to = current_time, by = 60)  # 过去1小时，每分钟一个点

    # 为每种监控类型生成数据
    for (monitor_type in monitor_config$types) {
      data_file <- file.path(results_dir, paste0(monitor_type, "_data.csv"))

      # 生成模拟数据
      simulation_data <- switch(monitor_type,
        "tic" = generate_tic_data(time_points),
        "bpc" = generate_bpc_data(time_points),
        "mz" = generate_mz_data(time_points, monitor_config$ions),
        "rsd" = generate_rsd_data(time_points),
        "eic" = generate_eic_data(time_points, monitor_config$ions),
        data.frame(时间 = format(time_points, "%H:%M:%S"), 值 = runif(length(time_points), 1000, 10000))
      )

      # 保存数据
      write.csv(simulation_data, data_file, row.names = FALSE, fileEncoding = "UTF-8")
      log_info(paste("已生成", monitor_type, "模拟数据:", data_file))
    }

  }, error = function(e) {
    log_error(paste("生成模拟数据失败:", e$message))
  })
}

# 生成TIC数据
generate_tic_data <- function(time_points) {
  base_intensity <- 5000000
  noise_factor <- 0.1
  trend_factor <- 0.02

  values <- base_intensity +
    base_intensity * noise_factor * rnorm(length(time_points)) +
    base_intensity * trend_factor * sin(seq(0, 2*pi, length.out = length(time_points)))

  data.frame(
    时间 = format(time_points, "%H:%M:%S"),
    值 = pmax(values, 0),
    状态 = ifelse(abs(values - base_intensity) > base_intensity * 0.2, "异常", "正常"),
    stringsAsFactors = FALSE
  )
}

# 生成BPC数据
generate_bpc_data <- function(time_points) {
  base_intensity <- 2000000
  noise_factor <- 0.15

  values <- base_intensity + base_intensity * noise_factor * rnorm(length(time_points))

  data.frame(
    时间 = format(time_points, "%H:%M:%S"),
    值 = pmax(values, 0),
    状态 = ifelse(abs(values - base_intensity) > base_intensity * 0.25, "异常", "正常"),
    stringsAsFactors = FALSE
  )
}

# 生成m/z数据
generate_mz_data <- function(time_points, ions = NULL) {
  if (is.null(ions) || length(ions) == 0) {
    # 默认m/z值
    mz_values <- c(118.0865, 322.0481)
  } else {
    mz_values <- sapply(ions, function(x) x$mz)
  }

  # 为每个m/z生成数据
  all_data <- data.frame()
  for (mz in mz_values) {
    base_intensity <- 100000
    noise_factor <- 0.2

    values <- base_intensity + base_intensity * noise_factor * rnorm(length(time_points))

    mz_data <- data.frame(
      时间 = format(time_points, "%H:%M:%S"),
      mz = mz,
      值 = pmax(values, 0),
      状态 = ifelse(abs(values - base_intensity) > base_intensity * 0.3, "异常", "正常"),
      stringsAsFactors = FALSE
    )

    all_data <- rbind(all_data, mz_data)
  }

  return(all_data)
}

# 生成RSD数据
generate_rsd_data <- function(time_points) {
  base_rsd <- 5  # 5% RSD
  noise_factor <- 0.3

  values <- base_rsd + base_rsd * noise_factor * rnorm(length(time_points))

  data.frame(
    时间 = format(time_points, "%H:%M:%S"),
    值 = pmax(values, 0),
    状态 = ifelse(values > 15, "异常", "正常"),  # RSD > 15% 为异常
    stringsAsFactors = FALSE
  )
}

# 生成EIC数据
generate_eic_data <- function(time_points, ions = NULL) {
  # EIC数据类似于m/z数据，但强度通常更低
  if (is.null(ions) || length(ions) == 0) {
    mz_values <- c(118.0865, 322.0481)
  } else {
    mz_values <- sapply(ions, function(x) x$mz)
  }

  all_data <- data.frame()
  for (mz in mz_values) {
    base_intensity <- 50000  # EIC强度通常比总离子流低
    noise_factor <- 0.25

    values <- base_intensity + base_intensity * noise_factor * rnorm(length(time_points))

    eic_data <- data.frame(
      时间 = format(time_points, "%H:%M:%S"),
      mz = mz,
      值 = pmax(values, 0),
      状态 = ifelse(abs(values - base_intensity) > base_intensity * 0.4, "异常", "正常"),
      stringsAsFactors = FALSE
    )

    all_data <- rbind(all_data, eic_data)
  }

  return(all_data)
}