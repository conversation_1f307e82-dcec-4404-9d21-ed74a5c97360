# Utils层 - 监控离子配置管理器
# 负责处理YAML格式的监控离子配置文件

# 检查yaml包是否已安装
if (!requireNamespace("yaml", quietly = TRUE)) {
  install.packages("yaml")
}
library(yaml)

# 监控离子配置类
MonitorIonsConfig <- setRefClass("MonitorIonsConfig",
  fields = list(
    compounds = "list",
    global_settings = "list",
    instrument_settings = "list"
  ),
  methods = list(
    # 从YAML文件加载配置
    load_from_yaml = function(file_path) {
      tryCatch({
        if (!file.exists(file_path)) {
          stop("配置文件不存在")
        }
        
        config_data <- yaml::read_yaml(file_path, fileEncoding = "UTF-8")
        
        # 验证配置结构
        if (!"monitor_ions" %in% names(config_data)) {
          stop("配置文件缺少 'monitor_ions' 部分")
        }
        
        # 设置化合物配置
        compounds <<- config_data$monitor_ions
        
        # 设置默认全局配置
        global_settings <<- list(
          default_tolerance = 0.01,
          default_collision_energy = 20,
          retention_time_window = 0.5,
          min_intensity = 1000,
          max_intensity = 1e6
        )
        
        # 设置默认仪器配置
        instrument_settings <<- list(
          mass_analyzer = "QqQ",
          ionization_mode = "ESI",
          scan_type = "MRM",
          dwell_time = 50,
          cycle_time = 1000
        )
        
        log_info(paste("监控离子配置已加载:", file_path))
        log_info(paste("加载了", length(compounds), "个化合物的配置"))
        
        return(TRUE)
      }, error = function(e) {
        log_error(paste("加载监控离子配置失败:", e$message))
        return(FALSE)
      })
    },
    
    # 保存配置到YAML文件
    save_to_yaml = function(file_path) {
      tryCatch({
        config_data <- list(
          monitor_ions = compounds,
          global_settings = global_settings,
          instrument_settings = instrument_settings
        )
        
        yaml::write_yaml(config_data, file_path, fileEncoding = "UTF-8")
        log_info(paste("监控离子配置已保存:", file_path))
        return(TRUE)
      }, error = function(e) {
        log_error(paste("保存监控离子配置失败:", e$message))
        return(FALSE)
      })
    },
    
    # 添加化合物配置
    add_compound = function(compound_config) {
      # 验证化合物配置
      required_fields <- c("compound_name", "molecular_weight", "scan_mode", "retention_time", "monitor_ions")
      missing_fields <- setdiff(required_fields, names(compound_config))

      if (length(missing_fields) > 0) {
        stop(paste("化合物配置缺少必要字段:", paste(missing_fields, collapse = ", ")))
      }

      # 检查化合物名称是否已存在，如果存在则覆盖
      existing_names <- sapply(compounds, function(x) x$compound_name)
      existing_index <- which(existing_names == compound_config$compound_name)

      if (length(existing_index) > 0) {
        # 覆盖已存在的化合物
        compounds[[existing_index[1]]] <<- compound_config
        log_info(paste("已覆盖化合物配置:", compound_config$compound_name))
      } else {
        # 添加新的化合物
        compounds <<- c(compounds, list(compound_config))
        log_info(paste("已添加化合物配置:", compound_config$compound_name))
      }
    },

    # 获取所有化合物名称
    get_compound_names = function() {
      if (length(compounds) == 0) return(character())
      sapply(compounds, function(x) x$compound_name)
    },

    # 清空所有配置
    clear_all = function() {
      compounds <<- list()
      log_info("已清空所有监控离子配置")
    },

    # 获取所有化合物配置
    get_all_compounds = function() {
      return(compounds)
    },
    
    # 删除化合物配置
    remove_compound = function(compound_name) {
      compound_indices <- which(sapply(compounds, function(x) x$compound_name == compound_name))
      if (length(compound_indices) > 0) {
        compounds <<- compounds[-compound_indices]
        log_info(paste("已删除化合物配置:", compound_name))
        return(TRUE)
      } else {
        log_warning(paste("未找到化合物:", compound_name))
        return(FALSE)
      }
    },
    
    # 获取化合物配置
    get_compound = function(compound_name) {
      compound_indices <- which(sapply(compounds, function(x) x$compound_name == compound_name))
      if (length(compound_indices) > 0) {
        return(compounds[[compound_indices[1]]])
      } else {
        return(NULL)
      }
    },
    
    # 获取所有化合物名称
    get_compound_names = function() {
      sapply(compounds, function(x) x$compound_name)
    },
    
    # 获取所有监控离子
    get_all_monitor_ions = function() {
      all_ions <- list()
      for (compound in compounds) {
        for (ion in compound$monitor_ions) {
          all_ions[[length(all_ions) + 1]] <- list(
            compound_name = compound$compound_name,
            mz = ion$mz,
            retention_time = compound$retention_time,
            scan_mode = compound$scan_mode
          )
        }
      }
      return(all_ions)
    },
    
    # 验证配置
    validate_config = function() {
      errors <- list()
      
      # 验证化合物配置
      for (i in seq_along(compounds)) {
        compound <- compounds[[i]]
        
        # 检查必要字段
        if (is.null(compound$compound_name) || compound$compound_name == "") {
          errors[[length(errors) + 1]] <- paste("化合物", i, ": 缺少化合物名称")
        }
        
        if (is.null(compound$molecular_weight) || compound$molecular_weight <= 0) {
          errors[[length(errors) + 1]] <- paste("化合物", compound$compound_name, ": 分子质量无效")
        }
        
        if (is.null(compound$retention_time) || compound$retention_time < 0) {
          errors[[length(errors) + 1]] <- paste("化合物", compound$compound_name, ": 保留时间无效")
        }
        
        if (is.null(compound$monitor_ions) || length(compound$monitor_ions) == 0) {
          errors[[length(errors) + 1]] <- paste("化合物", compound$compound_name, ": 缺少监控离子")
        } else {
          # 验证监控离子
          for (j in seq_along(compound$monitor_ions)) {
            ion <- compound$monitor_ions[[j]]
            if (is.null(ion$mz) || ion$mz <= 0) {
              errors[[length(errors) + 1]] <- paste("化合物", compound$compound_name, "离子", j, ": 质荷比无效")
            }
          }
        }
      }
      
      return(errors)
    },
    
    # 获取配置摘要
    get_summary = function() {
      summary <- list(
        total_compounds = length(compounds),
        compound_names = get_compound_names(),
        total_monitor_ions = length(get_all_monitor_ions()),
        global_settings = global_settings,
        instrument_settings = instrument_settings
      )
      return(summary)
    }
  )
)

# 创建监控离子配置实例
monitor_ions_config <- MonitorIonsConfig$new()

# 加载默认模板
load_default_template <- function() {
  template_path <- file.path(GLOBAL_CONFIG$data_path, "config", "monitor_ions_template.yaml")
  if (file.exists(template_path)) {
    monitor_ions_config$load_from_yaml(template_path)
  }
}

# 获取监控离子表格数据（用于UI显示）
get_monitor_ions_dataframe <- function() {
  if (length(monitor_ions_config$compounds) == 0) {
    # 返回空的数据框
    return(data.frame(
      化合物名称 = character(0),
      分子质量 = numeric(0),
      保留时间 = numeric(0),
      扫描模式 = character(0),
      离子化模式 = character(0),
      离子质荷比 = numeric(0),
      离子类型 = character(0),
      MS级别 = character(0),
      备注 = character(0),
      stringsAsFactors = FALSE
    ))
  }

  # 构建表格数据
  rows <- list()

  for (compound in monitor_ions_config$compounds) {
    if (length(compound$monitor_ions) > 0) {
      for (ion in compound$monitor_ions) {
        row <- list(
          化合物名称 = compound$compound_name %||% "",
          分子质量 = compound$molecular_weight %||% 0,
          保留时间 = compound$retention_time %||% 0,
          扫描模式 = compound$scan_mode %||% "",
          离子化模式 = compound$ionization_mode %||% "",
          离子质荷比 = ion$mz %||% 0,
          离子类型 = ion$ion_type %||% "",
          MS级别 = paste0("MS", ion$ms_level %||% 1),
          备注 = ion$notes %||% ""
        )
        rows[[length(rows) + 1]] <- row
      }
    } else {
      # 如果化合物没有监控离子，仍然显示化合物信息
      row <- list(
        化合物名称 = compound$compound_name %||% "",
        分子质量 = compound$molecular_weight %||% 0,
        保留时间 = compound$retention_time %||% 0,
        扫描模式 = compound$scan_mode %||% "",
        离子化模式 = compound$ionization_mode %||% "",
        离子质荷比 = 0,
        离子类型 = "",
        MS级别 = "",
        备注 = "无监控离子"
      )
      rows[[length(rows) + 1]] <- row
    }
  }

  # 转换为数据框
  if (length(rows) > 0) {
    df <- do.call(rbind, lapply(rows, data.frame, stringsAsFactors = FALSE))
    return(df)
  } else {
    return(data.frame(
      化合物名称 = character(0),
      分子质量 = numeric(0),
      保留时间 = numeric(0),
      扫描模式 = character(0),
      离子化模式 = character(0),
      离子质荷比 = numeric(0),
      离子类型 = character(0),
      MS级别 = character(0),
      备注 = character(0),
      stringsAsFactors = FALSE
    ))
  }
}

# 获取监控离子统计信息
get_monitor_ions_stats <- function() {
  total_compounds <- length(monitor_ions_config$compounds)
  total_ions <- 0
  scan_modes <- character()

  for (compound in monitor_ions_config$compounds) {
    total_ions <- total_ions + length(compound$monitor_ions)
    if (!is.null(compound$scan_mode)) {
      scan_modes <- c(scan_modes, compound$scan_mode)
    }
  }

  unique_scan_modes <- length(unique(scan_modes))

  return(list(
    total_compounds = total_compounds,
    total_ions = total_ions,
    unique_scan_modes = unique_scan_modes,
    scan_modes = unique(scan_modes)
  ))
}

# 从项目路径加载监控离子配置
load_project_monitor_ions <- function() {
  project_root <- get_project_root_path()
  if (is.null(project_root)) {
    return(FALSE)
  }

  config_path <- file.path(project_root, "config", "monitor_ions.yaml")
  if (file.exists(config_path)) {
    return(monitor_ions_config$load_from_yaml(config_path))
  } else {
    # 创建默认配置文件
    create_default_monitor_ions_config(config_path)
    return(TRUE)
  }
}

# 保存监控离子数据到项目data文件夹（使用YAML格式）
save_project_monitor_ions_data <- function(data_frame) {
  project_root <- get_project_root_path()
  if (is.null(project_root)) {
    stop("项目路径未设置")
  }

  # 确保data目录存在
  data_dir <- file.path(project_root, "data")
  if (!dir.exists(data_dir)) {
    dir.create(data_dir, recursive = TRUE)
  }

  data_path <- file.path(data_dir, "monitor_ions_data.yaml")

  tryCatch({
    if (nrow(data_frame) > 0) {
      # 为每行添加唯一ID
      if (!"ID" %in% colnames(data_frame)) {
        data_frame$ID <- paste0("ion_", seq_len(nrow(data_frame)))
      }

      # 转换为列表格式以便YAML保存
      data_list <- list()
      for (i in 1:nrow(data_frame)) {
        row <- data_frame[i, ]
        data_list[[i]] <- list(
          ID = row$ID,
          化合物名称 = row$化合物名称,
          分子质量 = row$分子质量,
          保留时间 = row$保留时间,
          扫描模式 = row$扫描模式,
          离子化模式 = row$离子化模式,
          离子质荷比 = row$离子质荷比,
          离子类型 = row$离子类型,
          MS级别 = row$MS级别,
          备注 = row$备注,
          创建时间 = as.character(Sys.time())
        )
      }

      yaml_content <- list(
        monitor_ions_data = data_list,
        metadata = list(
          total_count = nrow(data_frame),
          last_updated = as.character(Sys.time()),
          version = "1.0"
        )
      )
    } else {
      # 空数据
      yaml_content <- list(
        monitor_ions_data = list(),
        metadata = list(
          total_count = 0,
          last_updated = as.character(Sys.time()),
          version = "1.0"
        )
      )
    }

    yaml::write_yaml(yaml_content, data_path, fileEncoding = "UTF-8")
    log_info(paste("监控离子数据已保存到:", data_path))
    return(TRUE)

  }, error = function(e) {
    log_error(paste("保存监控离子数据失败:", e$message))
    return(FALSE)
  })
}

# 从项目data文件夹加载监控离子数据
load_project_monitor_ions_data <- function() {
  project_root <- get_project_root_path()
  if (is.null(project_root)) {
    return(data.frame(
      ID = character(0),
      化合物名称 = character(0),
      分子质量 = numeric(0),
      保留时间 = numeric(0),
      扫描模式 = character(0),
      离子化模式 = character(0),
      离子质荷比 = numeric(0),
      离子类型 = character(0),
      MS级别 = character(0),
      备注 = character(0),
      stringsAsFactors = FALSE
    ))
  }

  data_path <- file.path(project_root, "data", "monitor_ions_data.yaml")

  if (!file.exists(data_path)) {
    log_info("监控离子数据文件不存在，返回空数据框")
    return(data.frame(
      ID = character(0),
      化合物名称 = character(0),
      分子质量 = numeric(0),
      保留时间 = numeric(0),
      扫描模式 = character(0),
      离子化模式 = character(0),
      离子质荷比 = numeric(0),
      离子类型 = character(0),
      MS级别 = character(0),
      备注 = character(0),
      stringsAsFactors = FALSE
    ))
  }

  tryCatch({
    yaml_content <- yaml::read_yaml(data_path, fileEncoding = "UTF-8")

    if (length(yaml_content$monitor_ions_data) == 0) {
      return(data.frame(
        ID = character(0),
        化合物名称 = character(0),
        分子质量 = numeric(0),
        保留时间 = numeric(0),
        扫描模式 = character(0),
        离子化模式 = character(0),
        离子质荷比 = numeric(0),
        离子类型 = character(0),
        MS级别 = character(0),
        备注 = character(0),
        stringsAsFactors = FALSE
      ))
    }

    # 转换为数据框
    rows <- list()
    for (item in yaml_content$monitor_ions_data) {
      row <- list(
        ID = item$ID %||% paste0("ion_", length(rows) + 1),
        化合物名称 = item$化合物名称 %||% "",
        分子质量 = item$分子质量 %||% 0,
        保留时间 = item$保留时间 %||% 0,
        扫描模式 = item$扫描模式 %||% "DDA",
        离子化模式 = item$离子化模式 %||% "positive",
        离子质荷比 = item$离子质荷比 %||% 0,
        离子类型 = item$离子类型 %||% "[M+H]+",
        MS级别 = item$MS级别 %||% "MS1",
        备注 = item$备注 %||% ""
      )
      rows[[length(rows) + 1]] <- row
    }

    if (length(rows) > 0) {
      result <- do.call(rbind, lapply(rows, data.frame, stringsAsFactors = FALSE))
      log_info(paste("从项目加载了", nrow(result), "个监控离子"))
      return(result)
    } else {
      return(data.frame(
        ID = character(0),
        化合物名称 = character(0),
        分子质量 = numeric(0),
        保留时间 = numeric(0),
        扫描模式 = character(0),
        离子化模式 = character(0),
        离子质荷比 = numeric(0),
        离子类型 = character(0),
        MS级别 = character(0),
        备注 = character(0),
        stringsAsFactors = FALSE
      ))
    }

  }, error = function(e) {
    log_error(paste("加载监控离子数据失败:", e$message))
    return(data.frame(
      ID = character(0),
      化合物名称 = character(0),
      分子质量 = numeric(0),
      保留时间 = numeric(0),
      扫描模式 = character(0),
      离子化模式 = character(0),
      离子质荷比 = numeric(0),
      离子类型 = character(0),
      MS级别 = character(0),
      备注 = character(0),
      stringsAsFactors = FALSE
    ))
  })
}

# 保存监控离子配置到项目路径（保留原有功能）
save_project_monitor_ions <- function() {
  project_root <- get_project_root_path()
  if (is.null(project_root)) {
    stop("项目路径未设置")
  }

  # 确保config目录存在
  config_dir <- file.path(project_root, "config")
  if (!dir.exists(config_dir)) {
    dir.create(config_dir, recursive = TRUE)
  }

  config_path <- file.path(config_dir, "monitor_ions.yaml")
  return(monitor_ions_config$save_to_yaml(config_path))
}

# 计算分子离子峰质荷比（只支持常用离子类型）
calculate_molecular_ion_mz <- function(molecular_weight, ion_type = "[M+H]+") {
  switch(ion_type,
    "[M+H]+" = molecular_weight + 1.007276,
    "[M-H]-" = molecular_weight - 1.007276,
    molecular_weight  # 默认返回分子质量
  )
}

# 获取常用离子类型（简化版）
get_common_ion_types <- function() {
  list(
    positive = c("[M+H]+"),
    negative = c("[M-H]-")
  )
}

# 创建默认监控离子配置（基于DDA模式）
create_default_monitor_ions_config <- function(file_path = NULL) {
  default_config <- list(
    monitor_ions = list(
      list(
        compound_name = "咖啡因",
        molecular_weight = 194.19,
        retention_time = 3.2,
        scan_mode = "DDA",
        ionization_mode = "positive",
        monitor_ions = list(
          list(
            mz = calculate_molecular_ion_mz(194.19, "[M+H]+"),
            ion_type = "[M+H]+",
            ms_level = 1,
            notes = "分子离子峰"
          )
        )
      ),
      list(
        compound_name = "阿司匹林",
        molecular_weight = 180.16,
        retention_time = 5.5,
        scan_mode = "DDA",
        ionization_mode = "negative",
        monitor_ions = list(
          list(
            mz = calculate_molecular_ion_mz(180.16, "[M-H]-"),
            ion_type = "[M-H]-",
            ms_level = 1,
            notes = "分子离子峰"
          )
        )
      )
    ),
    global_settings = list(
      default_tolerance = 0.01,
      retention_time_window = 0.5,
      min_intensity = 1000,
      max_intensity = 1e6,
      default_ionization_mode = "positive"
    ),
    instrument_settings = list(
      mass_analyzer = "Orbitrap",
      ionization_mode = "ESI",
      scan_type = "DDA",
      ms1_resolution = 70000,
      ms2_resolution = 17500,
      collision_energy_mode = "HCD"
    )
  )

  if (!is.null(file_path)) {
    yaml::write_yaml(default_config, file_path, fileEncoding = "UTF-8")
    log_info(paste("已创建默认监控离子配置:", file_path))
  }

  # 加载到当前配置中
  monitor_ions_config$compounds <- default_config$monitor_ions
  monitor_ions_config$global_settings <- default_config$global_settings
  monitor_ions_config$instrument_settings <- default_config$instrument_settings

  return(default_config)
}

# 空值合并操作符
`%||%` <- function(x, y) {
  if (is.null(x) || length(x) == 0 || (is.character(x) && x == "")) y else x
}

# 导出函数
export_monitor_ions_config <- function() {
  return(monitor_ions_config)
}