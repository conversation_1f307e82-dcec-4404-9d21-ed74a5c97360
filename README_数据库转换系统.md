# 质谱数据库转换系统

## 概述

本系统用于将质谱分析产生的 `_spectra.rds` 文件转换为结构化的SQLite数据库格式，以支持实验室实时质控分析。系统基于质谱数据的特点，将MS1和MS2级别的数据分别存储在不同的表中，便于后续的质控分析和特征开发。

## 系统架构

### 核心组件

1. **数据库结构设计** (`utils/database_schema.R`)
   - 定义数据库表结构
   - 创建索引和外键约束
   - 支持MS1和MS2数据的分离存储

2. **数据提取器** (`utils/data_extractor.R`)
   - 从Spectra对象提取spectraData和peaksData
   - 按MS级别组织数据
   - 处理MsExperiment和Spectra对象

3. **数据库转换器** (`utils/database_converter.R`)
   - 执行完整的转换流程
   - 批量处理多个_spectra.rds文件
   - 维护数据完整性和一致性

4. **数据验证器** (`utils/data_validator.R`)
   - 验证数据库完整性
   - 检查外键约束
   - 数据质量评估

5. **测试脚本** (`test_database_conversion.R`)
   - 完整的转换测试流程
   - 生成详细的测试报告
   - 验证查询功能

## 数据库结构

### 表结构设计

#### 1. data_files (数据文件索引表)
- `file_id`: 主键，自增
- `file_name`: 文件名
- `file_path`: 文件路径
- `sample_type`: 样本类型 (QC, STD, BLANK, SAMPLE)
- `scan_mode`: 扫描模式 (positive, negative)
- `total_spectra`: 总谱图数
- `ms1_count`: MS1谱图数
- `ms2_count`: MS2谱图数
- `created_time`: 创建时间
- `last_updated`: 最后更新时间

#### 2. ms1_spectra_data (MS1谱图元数据表)
- `spectrum_id`: 主键，自增
- `file_id`: 外键，关联data_files表
- `scan_index`: 扫描索引
- `ms_level`: MS级别 (固定为1)
- `rtime`: 保留时间
- `acquisition_num`: 采集编号
- `polarity`: 极性 (1为正离子，0为负离子)
- `peaks_count`: 峰数量
- `tot_ion_current`: 总离子流
- `base_peak_mz`: 基峰m/z
- `base_peak_intensity`: 基峰强度
- `low_mz`: 最低m/z
- `high_mz`: 最高m/z
- 其他技术参数...

#### 3. ms1_peaks_data (MS1峰数据表)
- `peak_id`: 主键，自增
- `spectrum_id`: 外键，关联ms1_spectra_data表
- `mz`: 质荷比
- `intensity`: 强度

#### 4. ms2_spectra_data (MS2谱图元数据表)
- 包含MS1表的所有字段
- 额外的MS2特有字段：
  - `prec_scan_num`: 前体离子扫描编号
  - `precursor_mz`: 前体离子m/z
  - `precursor_intensity`: 前体离子强度
  - `precursor_charge`: 前体离子电荷
  - `collision_energy`: 碰撞能
  - `isolation_window_*`: 隔离窗口参数

#### 5. ms2_peaks_data (MS2峰数据表)
- 结构与ms1_peaks_data相同
- `spectrum_id`关联ms2_spectra_data表

### 索引设计

系统为关键字段创建了优化索引：
- 文件ID索引：快速按文件查询
- 保留时间索引：支持时间范围查询
- m/z索引：支持质量范围查询
- 强度索引：支持强度过滤
- 复合索引：优化常用查询组合

## 使用方法

### 1. 基本转换流程

```r
# 加载转换器
source("utils/database_converter.R")

# 执行转换
result <- convert_project_spectra("test/test4")

# 检查结果
if (result$success) {
  cat("转换成功！数据库位置:", result$database_path, "\n")
} else {
  cat("转换失败！\n")
}
```

### 2. 自定义转换

```r
# 指定具体路径
cache_dir <- "path/to/cache/spectra_v2"
output_db <- "path/to/output/database.sqlite"

convert_spectra_to_database(cache_dir, output_db)
```

### 3. 数据验证

```r
# 加载验证器
source("utils/data_validator.R")

# 验证数据库
validation_result <- validate_database_integrity("path/to/database.sqlite")

if (validation_result$valid) {
  cat("数据库验证通过\n")
} else {
  cat("验证失败，错误:", validation_result$errors, "\n")
}
```

### 4. 运行完整测试

```r
# 在R中运行
source("test_database_conversion.R")
test_result <- test_database_conversion()
```

## 数据查询示例

### 基本查询

```sql
-- 查看所有文件统计
SELECT file_name, sample_type, scan_mode, total_spectra, ms1_count, ms2_count
FROM data_files;

-- 查看MS1谱图的保留时间分布
SELECT MIN(rtime) as min_rt, MAX(rtime) as max_rt, AVG(rtime) as avg_rt
FROM ms1_spectra_data;
```

### 质控分析查询

```sql
-- 获取QC样本的TIC趋势
SELECT df.file_name, ms1.rtime, ms1.tot_ion_current
FROM data_files df
JOIN ms1_spectra_data ms1 ON df.file_id = ms1.file_id
WHERE df.sample_type = 'QC'
ORDER BY df.file_name, ms1.rtime;

-- 分析特定m/z范围的峰强度分布
SELECT mz, intensity
FROM ms1_peaks_data
WHERE mz BETWEEN 100 AND 500
ORDER BY intensity DESC
LIMIT 1000;
```

### 高级分析查询

```sql
-- 每个文件的数据质量统计
SELECT 
  df.file_name,
  df.sample_type,
  COUNT(DISTINCT ms1.spectrum_id) as ms1_spectra,
  COUNT(DISTINCT mp1.peak_id) as ms1_peaks,
  AVG(ms1.peaks_count) as avg_peaks_per_spectrum,
  AVG(ms1.tot_ion_current) as avg_tic
FROM data_files df
LEFT JOIN ms1_spectra_data ms1 ON df.file_id = ms1.file_id
LEFT JOIN ms1_peaks_data mp1 ON ms1.spectrum_id = mp1.spectrum_id
GROUP BY df.file_id, df.file_name, df.sample_type;
```

## 性能优化

### 数据库优化
- 使用事务批量插入数据
- 创建适当的索引
- 启用外键约束确保数据完整性
- 使用PRAGMA优化SQLite性能

### 内存管理
- 分批处理大文件
- 及时释放R对象
- 使用流式处理避免内存溢出

## 故障排除

### 常见问题

1. **包依赖问题**
   ```r
   # 安装必需的包
   install.packages(c("Spectra", "MsExperiment", "DBI", "RSQLite", "dplyr", "jsonlite"))
   ```

2. **文件路径问题**
   - 确保_spectra.rds文件存在于指定的缓存目录
   - 检查文件权限和路径格式

3. **内存不足**
   - 减少批处理大小
   - 增加系统内存
   - 使用64位R版本

4. **数据格式问题**
   - 验证_spectra.rds文件包含有效的Spectra或MsExperiment对象
   - 检查数据完整性

### 日志和调试

系统提供详细的日志输出：
- 文件处理进度
- 数据统计信息
- 错误和警告信息
- 性能指标

## 扩展功能

### 未来改进方向

1. **并行处理**：支持多线程转换大量文件
2. **增量更新**：支持增量添加新数据
3. **数据压缩**：优化存储空间使用
4. **Web界面**：提供图形化操作界面
5. **实时监控**：监控转换进度和系统状态

### 自定义扩展

系统设计为模块化，可以轻松扩展：
- 添加新的数据验证规则
- 支持其他数据库后端
- 集成额外的质谱数据格式
- 添加自定义分析功能

## 技术规格

- **R版本要求**: >= 4.0.0
- **主要依赖**: Spectra, MsExperiment, DBI, RSQLite
- **数据库**: SQLite 3.x
- **支持平台**: Windows, Linux, macOS
- **内存建议**: >= 8GB (处理大文件时)
- **存储空间**: 约为原始RDS文件的1.5-2倍
