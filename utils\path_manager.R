# 路径管理模块
# 统一管理三种路径类型：项目相对路径、原始数据绝对路径、软件安装相对路径

# 全局路径变量
APP_ROOT_PATH <- NULL      # 软件安装路径（app.R所在路径）
PROJECT_ROOT_PATH <- NULL  # 当前项目根路径
ORIGINAL_WD <- NULL        # 原始工作目录

# 初始化路径管理器
init_path_manager <- function() {
  # 获取软件安装路径（app.R所在路径）
  if (is.null(APP_ROOT_PATH)) {
    # 方式1: 检查全局变量（多种方式）
    app_root_candidates <- c()

    # 检查.GlobalEnv中的APP_ROOT_PATH
    if (exists("APP_ROOT_PATH", envir = .GlobalEnv)) {
      candidate <- get("APP_ROOT_PATH", envir = .GlobalEnv)
      if (!is.null(candidate) && candidate != "") {
        app_root_candidates <- c(app_root_candidates, candidate)
      }
    }

    # 检查parent.frame()中的APP_ROOT_PATH
    tryCatch({
      if (exists("APP_ROOT_PATH", envir = parent.frame())) {
        candidate <- get("APP_ROOT_PATH", envir = parent.frame())
        if (!is.null(candidate) && candidate != "") {
          app_root_candidates <- c(app_root_candidates, candidate)
        }
      }
    }, error = function(e) {})

    # 方式2: 从当前工作目录向上查找app.R
    current_dir <- getwd()
    search_dir <- current_dir
    for (i in 1:5) {
      if (file.exists(file.path(search_dir, "app.R"))) {
        app_root_candidates <- c(app_root_candidates, search_dir)
        break
      }
      parent_dir <- dirname(search_dir)
      if (parent_dir == search_dir) break
      search_dir <- parent_dir
    }

    # 方式3: 使用命令行参数
    if (exists("commandArgs")) {
      args <- commandArgs(trailingOnly = FALSE)
      file_arg <- grep("--file=", args, value = TRUE)
      if (length(file_arg) > 0) {
        script_path <- sub("--file=", "", file_arg[1])
        candidate <- dirname(normalizePath(script_path))
        app_root_candidates <- c(app_root_candidates, candidate)
      }
    }

    # 方式4: 使用路径模式匹配
    if (grepl("实验室实时质控", current_dir)) {
      app_root_pattern <- "^(.+实验室实时质控)"
      matches <- regmatches(current_dir, regexpr(app_root_pattern, current_dir))
      if (length(matches) > 0) {
        candidate <- matches[1]
        if (file.exists(file.path(candidate, "app.R"))) {
          app_root_candidates <- c(app_root_candidates, candidate)
        }
      }
    }

    # 选择最佳候选路径
    for (candidate in app_root_candidates) {
      if (!is.null(candidate) && candidate != "" &&
          dir.exists(candidate) && file.exists(file.path(candidate, "app.R"))) {
        APP_ROOT_PATH <<- normalizePath(candidate)
        break
      }
    }

    # 如果所有方法都失败，使用当前工作目录
    if (is.null(APP_ROOT_PATH) || APP_ROOT_PATH == "") {
      APP_ROOT_PATH <<- getwd()
    }
  }

  # 保存原始工作目录
  if (is.null(ORIGINAL_WD)) {
    ORIGINAL_WD <<- getwd()
  }

  # 延迟日志记录，避免在logger加载前调用
  if (exists("log_info")) {
    log_info(paste("路径管理器初始化完成"))
    log_info(paste("软件安装路径:", APP_ROOT_PATH))
    log_info(paste("原始工作目录:", ORIGINAL_WD))
  }
}

# 获取软件安装路径
get_app_root_path <- function() {
  if (is.null(APP_ROOT_PATH) || APP_ROOT_PATH == "") {
    init_path_manager()
  }

  # 二次验证：确保路径有效
  if (is.null(APP_ROOT_PATH) || APP_ROOT_PATH == "" || !dir.exists(APP_ROOT_PATH)) {
    # 强制重新初始化
    APP_ROOT_PATH <<- NULL
    init_path_manager()
  }

  # 最终验证
  if (is.null(APP_ROOT_PATH) || APP_ROOT_PATH == "") {
    if (exists("log_error")) {
      log_error("无法获取有效的应用根路径")
    }
    return(NULL)
  }

  return(APP_ROOT_PATH)
}

# 获取软件安装路径下的相对路径
get_app_relative_path <- function(...) {
  app_root <- get_app_root_path()
  return(file.path(app_root, ...))
}

# 设置当前项目路径
set_project_root_path <- function(project_path) {
  # 保存原始工作目录（如果尚未保存）
  if (is.null(ORIGINAL_WD)) {
    ORIGINAL_WD <<- getwd()
  }

  PROJECT_ROOT_PATH <<- normalizePath(project_path)

  # 切换工作目录到项目路径
  setwd(PROJECT_ROOT_PATH)

  if (exists("log_info")) {
    log_info(paste("项目路径已设置:", PROJECT_ROOT_PATH))
    log_info(paste("工作目录已切换到:", getwd()))
    log_info(paste("原始工作目录已保存:", ORIGINAL_WD))
  }
}

# 获取当前项目路径
get_project_root_path <- function() {
  if (is.null(PROJECT_ROOT_PATH)) {
    # 如果没有设置项目路径，尝试从当前工作目录推断
    current_wd <- getwd()
    if (file.exists(file.path(current_wd, "config"))) {
      PROJECT_ROOT_PATH <<- current_wd
    }
  }
  return(PROJECT_ROOT_PATH)
}

# 获取项目内的相对路径
get_project_relative_path <- function(...) {
  # 直接使用相对路径（因为工作目录已切换到项目路径）
  return(file.path(...))
}

# 获取项目内的绝对路径
get_project_absolute_path <- function(...) {
  project_root <- get_project_root_path()
  if (is.null(project_root)) {
    stop("项目路径未设置，请先创建或导入项目")
  }
  return(file.path(project_root, ...))
}

# 验证路径是否存在
validate_path <- function(path, create_if_missing = FALSE) {
  if (is.null(path) || path == "") {
    return(FALSE)
  }
  
  if (file.exists(path) || dir.exists(path)) {
    return(TRUE)
  }
  
  if (create_if_missing) {
    tryCatch({
      if (grepl("\\.[a-zA-Z0-9]+$", basename(path))) {
        # 如果是文件路径，创建父目录
        dir.create(dirname(path), recursive = TRUE, showWarnings = FALSE)
      } else {
        # 如果是目录路径，创建目录
        dir.create(path, recursive = TRUE, showWarnings = FALSE)
      }
      return(TRUE)
    }, error = function(e) {
      if (exists("log_error")) {
        log_error(paste("创建路径失败:", path, "错误:", e$message))
      }
      return(FALSE)
    })
  }
  
  return(FALSE)
}

# 获取项目配置文件路径
get_project_config_path <- function(project_name = NULL) {
  if (is.null(project_name)) {
    # 使用当前项目的配置文件
    return(get_project_relative_path("config", "project.json"))
  } else {
    # 使用指定项目名的配置文件
    return(get_project_relative_path("config", paste0(project_name, ".json")))
  }
}

# 获取数据列表文件路径
get_data_list_path <- function() {
  return(get_project_relative_path("data", "data_list.csv"))
}

# 获取监控结果路径
get_monitoring_results_path <- function() {
  return(get_project_relative_path("results", "monitoring_results.csv"))
}

# 获取报告路径
get_report_path <- function(report_name) {
  return(get_project_relative_path("reports", report_name))
}

# 标准化绝对路径（用于原始数据路径）
normalize_absolute_path <- function(path) {
  if (is.null(path) || path == "") {
    return(NULL)
  }
  
  tryCatch({
    return(normalizePath(path, mustWork = FALSE))
  }, error = function(e) {
    if (exists("log_warning")) {
      log_warning(paste("路径标准化失败:", path))
    }
    return(path)
  })
}

# 检查是否为绝对路径
is_absolute_path <- function(path) {
  if (is.null(path) || path == "") {
    return(FALSE)
  }
  
  # Windows: 检查是否以盘符开头 (C:, D:, etc.)
  if (.Platform$OS.type == "windows") {
    return(grepl("^[A-Za-z]:", path))
  } else {
    # Unix/Linux: 检查是否以 / 开头
    return(grepl("^/", path))
  }
}

# 路径管理器状态检查
check_path_manager_status <- function() {
  status <- list(
    app_root_path = APP_ROOT_PATH,
    project_root_path = PROJECT_ROOT_PATH,
    current_wd = getwd(),
    original_wd = ORIGINAL_WD,
    project_config_exists = file.exists(get_project_config_path()),
    data_dir_exists = dir.exists(get_project_relative_path("data")),
    config_dir_exists = dir.exists(get_project_relative_path("config"))
  )
  
  return(status)
}

# 重置路径管理器（用于测试或重新初始化）
reset_path_manager <- function() {
  if (!is.null(ORIGINAL_WD)) {
    setwd(ORIGINAL_WD)
  }
  
  APP_ROOT_PATH <<- NULL
  PROJECT_ROOT_PATH <<- NULL
  
  if (exists("log_info")) {
    log_info("路径管理器已重置")
  }
}

# 安全的source函数，能处理工作目录切换
safe_source <- function(file_path, ...) {
  # 如果是绝对路径，直接使用
  if (is_absolute_path(file_path)) {
    source(file_path, ...)
    return(invisible(TRUE))
  }

  # 对于相对路径，按优先级尝试不同的基础路径
  base_paths <- c()

  # 1. 当前工作目录
  base_paths <- c(base_paths, getwd())

  # 2. 如果当前不在原始工作目录，尝试原始工作目录（软件安装路径）
  if (!is.null(ORIGINAL_WD) && getwd() != ORIGINAL_WD) {
    base_paths <- c(base_paths, ORIGINAL_WD)
  }

  # 3. 如果设置了APP_ROOT_PATH，也尝试使用它
  if (!is.null(APP_ROOT_PATH) && !APP_ROOT_PATH %in% base_paths) {
    base_paths <- c(base_paths, APP_ROOT_PATH)
  }

  # 按顺序尝试每个基础路径
  for (base_path in base_paths) {
    full_path <- file.path(base_path, file_path)
    if (file.exists(full_path)) {
      source(full_path, ...)
      if (exists("log_debug")) {
        log_debug(paste("成功加载文件:", full_path))
      }
      return(invisible(TRUE))
    }
  }

  # 如果所有路径都失败，尝试原始路径（可能会报错，但能提供有用的错误信息）
  source(file_path, ...)
  return(invisible(TRUE))
}

# 在应用启动时初始化
init_path_manager()
