# 数据验证器
# 确保数据转换过程中的数据完整性和一致性

# 加载必要的包
load_validation_packages <- function() {
  required_packages <- c("DBI", "RSQLite", "dplyr")
  
  for (pkg in required_packages) {
    if (!requireNamespace(pkg, quietly = TRUE)) {
      stop(paste("Required package", pkg, "is not installed. Please install it first."))
    }
  }
  
  library(DBI)
  library(RSQLite)
  library(dplyr)
}

# 验证数据库完整性
validate_database_integrity <- function(db_path) {
  cat("=== 数据库完整性验证 ===\n")
  
  if (!file.exists(db_path)) {
    return(list(
      valid = FALSE,
      message = "数据库文件不存在",
      errors = list("数据库文件不存在: " + db_path)
    ))
  }
  
  con <- DBI::dbConnect(RSQLite::SQLite(), db_path)
  errors <- character()
  warnings <- character()
  
  tryCatch({
    # 1. 验证表结构
    cat("1. 验证表结构...\n")
    schema_validation <- validate_table_schema(con)
    if (!schema_validation$valid) {
      errors <- c(errors, schema_validation$errors)
    }
    
    # 2. 验证外键约束
    cat("2. 验证外键约束...\n")
    fk_validation <- validate_foreign_keys(con)
    if (!fk_validation$valid) {
      errors <- c(errors, fk_validation$errors)
    }
    
    # 3. 验证数据一致性
    cat("3. 验证数据一致性...\n")
    consistency_validation <- validate_data_consistency(con)
    if (!consistency_validation$valid) {
      errors <- c(errors, consistency_validation$errors)
    }
    warnings <- c(warnings, consistency_validation$warnings)
    
    # 4. 验证数据完整性
    cat("4. 验证数据完整性...\n")
    completeness_validation <- validate_data_completeness(con)
    if (!completeness_validation$valid) {
      errors <- c(errors, completeness_validation$errors)
    }
    warnings <- c(warnings, completeness_validation$warnings)
    
    # 5. 验证数据质量
    cat("5. 验证数据质量...\n")
    quality_validation <- validate_data_quality(con)
    warnings <- c(warnings, quality_validation$warnings)
    
  }, finally = {
    DBI::dbDisconnect(con)
  })
  
  # 生成验证报告
  is_valid <- length(errors) == 0
  
  cat("\n=== 验证结果 ===\n")
  cat("数据库状态:", if (is_valid) "有效" else "无效", "\n")
  cat("错误数量:", length(errors), "\n")
  cat("警告数量:", length(warnings), "\n")
  
  if (length(errors) > 0) {
    cat("\n错误列表:\n")
    for (i in seq_along(errors)) {
      cat("  ", i, ".", errors[i], "\n")
    }
  }
  
  if (length(warnings) > 0) {
    cat("\n警告列表:\n")
    for (i in seq_along(warnings)) {
      cat("  ", i, ".", warnings[i], "\n")
    }
  }
  
  return(list(
    valid = is_valid,
    errors = errors,
    warnings = warnings,
    message = if (is_valid) "数据库验证通过" else "数据库验证失败"
  ))
}

# 验证表结构
validate_table_schema <- function(con) {
  errors <- character()
  
  # 期望的表列表
  expected_tables <- c("data_files", "ms1_spectra_data", "ms1_peaks_data", 
                      "ms2_spectra_data", "ms2_peaks_data")
  
  # 获取实际表列表
  actual_tables <- DBI::dbListTables(con)
  
  # 检查缺失的表
  missing_tables <- setdiff(expected_tables, actual_tables)
  if (length(missing_tables) > 0) {
    errors <- c(errors, paste("缺少表:", paste(missing_tables, collapse = ", ")))
  }
  
  # 检查每个表的列结构
  for (table in intersect(expected_tables, actual_tables)) {
    table_info <- DBI::dbGetQuery(con, paste("PRAGMA table_info(", table, ")"))
    
    # 根据表名验证特定列
    if (table == "data_files") {
      required_cols <- c("file_id", "file_name", "file_path", "sample_type", "scan_mode")
    } else if (table == "ms1_spectra_data") {
      required_cols <- c("spectrum_id", "file_id", "scan_index", "ms_level", "rtime")
    } else if (table == "ms1_peaks_data") {
      required_cols <- c("peak_id", "spectrum_id", "mz", "intensity")
    } else if (table == "ms2_spectra_data") {
      required_cols <- c("spectrum_id", "file_id", "scan_index", "ms_level", "precursor_mz")
    } else if (table == "ms2_peaks_data") {
      required_cols <- c("peak_id", "spectrum_id", "mz", "intensity")
    }
    
    actual_cols <- table_info$name
    missing_cols <- setdiff(required_cols, actual_cols)
    
    if (length(missing_cols) > 0) {
      errors <- c(errors, paste("表", table, "缺少列:", paste(missing_cols, collapse = ", ")))
    }
  }
  
  return(list(
    valid = length(errors) == 0,
    errors = errors
  ))
}

# 验证外键约束
validate_foreign_keys <- function(con) {
  errors <- character()
  
  # 检查ms1_spectra_data中的file_id是否都存在于data_files中
  orphan_ms1 <- DBI::dbGetQuery(con, "
    SELECT COUNT(*) as count 
    FROM ms1_spectra_data ms1 
    LEFT JOIN data_files df ON ms1.file_id = df.file_id 
    WHERE df.file_id IS NULL
  ")
  
  if (orphan_ms1$count > 0) {
    errors <- c(errors, paste("ms1_spectra_data中有", orphan_ms1$count, "条记录的file_id无效"))
  }
  
  # 检查ms2_spectra_data中的file_id是否都存在于data_files中
  orphan_ms2 <- DBI::dbGetQuery(con, "
    SELECT COUNT(*) as count 
    FROM ms2_spectra_data ms2 
    LEFT JOIN data_files df ON ms2.file_id = df.file_id 
    WHERE df.file_id IS NULL
  ")
  
  if (orphan_ms2$count > 0) {
    errors <- c(errors, paste("ms2_spectra_data中有", orphan_ms2$count, "条记录的file_id无效"))
  }
  
  # 检查ms1_peaks_data中的spectrum_id是否都存在于ms1_spectra_data中
  orphan_ms1_peaks <- DBI::dbGetQuery(con, "
    SELECT COUNT(*) as count 
    FROM ms1_peaks_data mp 
    LEFT JOIN ms1_spectra_data ms ON mp.spectrum_id = ms.spectrum_id 
    WHERE ms.spectrum_id IS NULL
  ")
  
  if (orphan_ms1_peaks$count > 0) {
    errors <- c(errors, paste("ms1_peaks_data中有", orphan_ms1_peaks$count, "条记录的spectrum_id无效"))
  }
  
  # 检查ms2_peaks_data中的spectrum_id是否都存在于ms2_spectra_data中
  orphan_ms2_peaks <- DBI::dbGetQuery(con, "
    SELECT COUNT(*) as count 
    FROM ms2_peaks_data mp 
    LEFT JOIN ms2_spectra_data ms ON mp.spectrum_id = ms.spectrum_id 
    WHERE ms.spectrum_id IS NULL
  ")
  
  if (orphan_ms2_peaks$count > 0) {
    errors <- c(errors, paste("ms2_peaks_data中有", orphan_ms2_peaks$count, "条记录的spectrum_id无效"))
  }
  
  return(list(
    valid = length(errors) == 0,
    errors = errors
  ))
}

# 验证数据一致性
validate_data_consistency <- function(con) {
  errors <- character()
  warnings <- character()
  
  # 验证文件统计信息的一致性
  file_stats <- DBI::dbGetQuery(con, "
    SELECT 
      df.file_id,
      df.file_name,
      df.total_spectra,
      df.ms1_count,
      df.ms2_count,
      COALESCE(ms1_actual.count, 0) as ms1_actual,
      COALESCE(ms2_actual.count, 0) as ms2_actual
    FROM data_files df
    LEFT JOIN (
      SELECT file_id, COUNT(*) as count 
      FROM ms1_spectra_data 
      GROUP BY file_id
    ) ms1_actual ON df.file_id = ms1_actual.file_id
    LEFT JOIN (
      SELECT file_id, COUNT(*) as count 
      FROM ms2_spectra_data 
      GROUP BY file_id
    ) ms2_actual ON df.file_id = ms2_actual.file_id
  ")
  
  for (i in 1:nrow(file_stats)) {
    row <- file_stats[i, ]
    
    # 检查MS1计数一致性
    if (row$ms1_count != row$ms1_actual) {
      errors <- c(errors, paste("文件", row$file_name, "的MS1计数不一致: 记录", row$ms1_count, "实际", row$ms1_actual))
    }
    
    # 检查MS2计数一致性
    if (row$ms2_count != row$ms2_actual) {
      errors <- c(errors, paste("文件", row$file_name, "的MS2计数不一致: 记录", row$ms2_count, "实际", row$ms2_actual))
    }
    
    # 检查总计数一致性
    total_actual <- row$ms1_actual + row$ms2_actual
    if (row$total_spectra != total_actual) {
      errors <- c(errors, paste("文件", row$file_name, "的总谱图计数不一致: 记录", row$total_spectra, "实际", total_actual))
    }
  }
  
  return(list(
    valid = length(errors) == 0,
    errors = errors,
    warnings = warnings
  ))
}

# 验证数据完整性
validate_data_completeness <- function(con) {
  errors <- character()
  warnings <- character()
  
  # 检查必需字段的空值
  null_checks <- list(
    "data_files" = c("file_name", "file_path"),
    "ms1_spectra_data" = c("file_id", "scan_index", "ms_level"),
    "ms1_peaks_data" = c("spectrum_id", "mz", "intensity"),
    "ms2_spectra_data" = c("file_id", "scan_index", "ms_level"),
    "ms2_peaks_data" = c("spectrum_id", "mz", "intensity")
  )
  
  for (table in names(null_checks)) {
    for (column in null_checks[[table]]) {
      null_count <- DBI::dbGetQuery(con, paste0(
        "SELECT COUNT(*) as count FROM ", table, " WHERE ", column, " IS NULL"
      ))
      
      if (null_count$count > 0) {
        errors <- c(errors, paste("表", table, "的列", column, "有", null_count$count, "个空值"))
      }
    }
  }
  
  # 检查数据范围合理性
  # MS Level应该只有1和2
  invalid_ms_levels <- DBI::dbGetQuery(con, "
    SELECT COUNT(*) as count 
    FROM (
      SELECT ms_level FROM ms1_spectra_data WHERE ms_level NOT IN (1, 2)
      UNION ALL
      SELECT ms_level FROM ms2_spectra_data WHERE ms_level NOT IN (1, 2)
    )
  ")
  
  if (invalid_ms_levels$count > 0) {
    errors <- c(errors, paste("发现", invalid_ms_levels$count, "个无效的MS Level值"))
  }
  
  return(list(
    valid = length(errors) == 0,
    errors = errors,
    warnings = warnings
  ))
}

# 验证数据质量
validate_data_quality <- function(con) {
  warnings <- character()
  
  # 检查异常的m/z值
  invalid_mz <- DBI::dbGetQuery(con, "
    SELECT COUNT(*) as count 
    FROM (
      SELECT mz FROM ms1_peaks_data WHERE mz <= 0 OR mz > 2000
      UNION ALL
      SELECT mz FROM ms2_peaks_data WHERE mz <= 0 OR mz > 2000
    )
  ")
  
  if (invalid_mz$count > 0) {
    warnings <- c(warnings, paste("发现", invalid_mz$count, "个异常的m/z值 (<=0 或 >2000)"))
  }
  
  # 检查异常的强度值
  invalid_intensity <- DBI::dbGetQuery(con, "
    SELECT COUNT(*) as count 
    FROM (
      SELECT intensity FROM ms1_peaks_data WHERE intensity < 0
      UNION ALL
      SELECT intensity FROM ms2_peaks_data WHERE intensity < 0
    )
  ")
  
  if (invalid_intensity$count > 0) {
    warnings <- c(warnings, paste("发现", invalid_intensity$count, "个负强度值"))
  }
  
  # 检查保留时间的合理性
  invalid_rtime <- DBI::dbGetQuery(con, "
    SELECT COUNT(*) as count 
    FROM (
      SELECT rtime FROM ms1_spectra_data WHERE rtime < 0 OR rtime > 7200
      UNION ALL
      SELECT rtime FROM ms2_spectra_data WHERE rtime < 0 OR rtime > 7200
    )
  ")
  
  if (invalid_rtime$count > 0) {
    warnings <- c(warnings, paste("发现", invalid_rtime$count, "个异常的保留时间值 (<0 或 >7200秒)"))
  }
  
  return(list(
    warnings = warnings
  ))
}

# 生成验证报告
generate_validation_report <- function(validation_result, output_file = NULL) {
  report <- list(
    timestamp = format(Sys.time(), "%Y-%m-%d %H:%M:%S"),
    validation_result = validation_result
  )
  
  if (!is.null(output_file)) {
    jsonlite::write_json(report, output_file, pretty = TRUE, auto_unbox = TRUE)
    cat("验证报告已保存到:", output_file, "\n")
  }
  
  return(report)
}
