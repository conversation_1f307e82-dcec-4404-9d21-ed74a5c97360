# UI层 - 主界面（重构版本）
# 实现项目选择页面和工作区页面的切换逻辑

# 加载UI模块
source("ui/project_selection_ui.R", encoding = "UTF-8")
source("ui/workspace_ui.R", encoding = "UTF-8")

ui <- fluidPage(
  # 页面基础设置
  tags$head(
    tags$meta(name = "viewport", content = "width=device-width, initial-scale=1, shrink-to-fit=no"),
    tags$title("实验室实时质控系统"),

    # 移除Bootstrap 5，使用Shiny默认的Bootstrap版本
    # 添加模态框修复的CSS和折叠功能CSS
    tags$style(HTML("
      .modal {
        z-index: 1050 !important;
      }
      .modal-backdrop {
        z-index: 1040 !important;
      }
      .modal.fade .modal-dialog {
        transition: transform 0.3s ease-out;
        transform: translate(0, -50px);
      }
      .modal.show .modal-dialog {
        transform: none;
      }

      /* 折叠功能样式 */
      .collapsible-header {
        transition: background-color 0.3s ease;
      }
      .collapsible-header:hover {
        background-color: #f8f9fa;
      }
      .collapse-icon {
        transition: transform 0.3s ease;
      }
      .collapse-icon.rotated {
        transform: rotate(180deg);
      }
      .module-content {
        transition: all 0.3s ease;
        overflow: hidden;
      }
      .module-content.collapsed {
        max-height: 0;
        padding-top: 0;
        padding-bottom: 0;
        margin-top: 0;
        margin-bottom: 0;
      }
    ")),

    # 添加折叠功能的JavaScript
    tags$script(HTML("
      function toggleCollapse(contentId) {
        var content = document.getElementById(contentId);
        var icon = document.getElementById(contentId.replace('_content', '_icon'));

        if (content.classList.contains('collapsed')) {
          // 展开
          content.classList.remove('collapsed');
          content.style.maxHeight = content.scrollHeight + 'px';
          if (icon) {
            icon.classList.remove('rotated');
          }

          // 动画完成后移除maxHeight限制
          setTimeout(function() {
            if (!content.classList.contains('collapsed')) {
              content.style.maxHeight = 'none';
            }
          }, 300);
        } else {
          // 折叠
          content.style.maxHeight = content.scrollHeight + 'px';
          // 强制重绘
          content.offsetHeight;
          content.classList.add('collapsed');
          if (icon) {
            icon.classList.add('rotated');
          }
        }
      }
    ")),
    tags$script(HTML("
      Shiny.addCustomMessageHandler('eic_extraction_done', function(msg) {
        // 自动刷新EIC结果表格
        Shiny.setInputValue('refresh_eic_results', Math.random());
        alert('EIC提取已完成！');
      });
    "))
  ),
  
  # 使用shinyjs
  useShinyjs(),
  
  # 暂时只显示项目选择页面，后续通过JavaScript切换
  div(id = "project_selection_page",
    project_selection_ui()
  ),

  div(id = "workspace_page", style = "display: none;",
    workspace_ui()
  )
)
