# 数据库修复和升级工具
# 用于修复现有数据库的结构问题和升级到新版本

# 加载必要的包
source("utils/database_schema.R")
source("utils/data_validator.R")

# 检查并修复数据库结构
repair_database_structure <- function(db_path) {
  cat("=== 数据库结构修复 ===\n")
  cat("数据库路径:", db_path, "\n")
  
  if (!file.exists(db_path)) {
    stop("数据库文件不存在:", db_path)
  }
  
  con <- DBI::dbConnect(RSQLite::SQLite(), db_path)
  
  tryCatch({
    # 启用外键约束
    DBI::dbExecute(con, "PRAGMA foreign_keys = ON;")
    
    # 备份原数据库
    backup_path <- paste0(db_path, ".backup.", format(Sys.time(), "%Y%m%d_%H%M%S"))
    file.copy(db_path, backup_path)
    cat("数据库已备份到:", backup_path, "\n")
    
    # 检查现有表结构
    existing_tables <- DBI::dbListTables(con)
    cat("现有表:", paste(existing_tables, collapse = ", "), "\n")
    
    # 获取期望的表结构
    expected_schema <- get_database_schema()
    
    # 修复每个表
    for (table_name in names(expected_schema)) {
      table_def <- expected_schema[[table_name]]
      repair_table_structure(con, table_def)
    }
    
    # 验证修复结果
    validation_result <- validate_database_integrity(db_path)
    
    if (validation_result$valid) {
      cat("数据库结构修复完成\n")
      return(list(success = TRUE, backup_path = backup_path))
    } else {
      cat("数据库结构修复后仍有问题:\n")
      for (error in validation_result$errors) {
        cat("  -", error, "\n")
      }
      return(list(success = FALSE, errors = validation_result$errors, backup_path = backup_path))
    }
    
  }, finally = {
    DBI::dbDisconnect(con)
  })
}

# 修复单个表结构
repair_table_structure <- function(con, table_def) {
  table_name <- table_def$table_name
  cat("修复表:", table_name, "\n")
  
  # 检查表是否存在
  table_exists <- DBI::dbExistsTable(con, table_name)
  
  if (!table_exists) {
    cat("  - 表不存在，创建新表\n")
    create_sql <- generate_create_table_sql(table_def)
    DBI::dbExecute(con, create_sql)
    
    # 创建索引
    if (!is.null(table_def$indexes)) {
      for (index_sql in table_def$indexes) {
        DBI::dbExecute(con, index_sql)
      }
    }
    return()
  }
  
  # 获取现有表结构
  table_info <- DBI::dbGetQuery(con, paste("PRAGMA table_info(", table_name, ")"))
  existing_columns <- table_info$name
  
  # 检查是否需要添加列
  expected_columns <- names(table_def$columns)
  # 过滤掉外键约束（不是实际列）
  expected_columns <- expected_columns[!grepl("^FOREIGN KEY", expected_columns)]
  
  missing_columns <- setdiff(expected_columns, existing_columns)
  
  if (length(missing_columns) > 0) {
    cat("  - 添加缺失的列:", paste(missing_columns, collapse = ", "), "\n")
    for (col in missing_columns) {
      col_def <- table_def$columns[[col]]
      # 移除约束，因为ALTER TABLE ADD COLUMN不支持复杂约束
      col_def_simple <- gsub(" CHECK\\([^)]+\\)", "", col_def)
      col_def_simple <- gsub(" REFERENCES [^,)]+", "", col_def_simple)
      
      alter_sql <- paste0("ALTER TABLE ", table_name, " ADD COLUMN ", col, " ", col_def_simple)
      tryCatch({
        DBI::dbExecute(con, alter_sql)
      }, error = function(e) {
        cat("    警告: 无法添加列", col, ":", e$message, "\n")
      })
    }
  }
  
  # 检查并创建缺失的索引
  if (!is.null(table_def$indexes)) {
    for (index_sql in table_def$indexes) {
      tryCatch({
        DBI::dbExecute(con, index_sql)
      }, error = function(e) {
        # 索引可能已存在，忽略错误
        if (!grepl("already exists", e$message, ignore.case = TRUE)) {
          cat("    警告: 无法创建索引:", e$message, "\n")
        }
      })
    }
  }
}

# 数据迁移函数（用于重大结构变更）
migrate_database_data <- function(old_db_path, new_db_path) {
  cat("=== 数据库数据迁移 ===\n")
  cat("源数据库:", old_db_path, "\n")
  cat("目标数据库:", new_db_path, "\n")
  
  # 创建新数据库
  create_database(new_db_path)
  
  old_con <- DBI::dbConnect(RSQLite::SQLite(), old_db_path)
  new_con <- DBI::dbConnect(RSQLite::SQLite(), new_db_path)
  
  tryCatch({
    # 启用外键约束
    DBI::dbExecute(new_con, "PRAGMA foreign_keys = ON;")
    
    # 迁移数据文件表
    migrate_data_files_table(old_con, new_con)
    
    # 迁移MS1数据
    migrate_ms1_data(old_con, new_con)
    
    # 迁移MS2数据
    migrate_ms2_data(old_con, new_con)
    
    cat("数据迁移完成\n")
    return(TRUE)
    
  }, finally = {
    DBI::dbDisconnect(old_con)
    DBI::dbDisconnect(new_con)
  })
}

# 迁移数据文件表
migrate_data_files_table <- function(old_con, new_con) {
  cat("迁移数据文件表...\n")
  
  # 检查旧表是否存在
  if (!DBI::dbExistsTable(old_con, "data_files")) {
    cat("  - 旧数据库中没有data_files表\n")
    return()
  }
  
  # 读取旧数据
  old_data <- DBI::dbGetQuery(old_con, "SELECT * FROM data_files")
  
  if (nrow(old_data) == 0) {
    cat("  - 没有数据需要迁移\n")
    return()
  }
  
  # 插入到新数据库
  DBI::dbWriteTable(new_con, "data_files", old_data, append = TRUE, row.names = FALSE)
  cat("  - 迁移了", nrow(old_data), "条记录\n")
}

# 迁移MS1数据
migrate_ms1_data <- function(old_con, new_con) {
  cat("迁移MS1数据...\n")
  
  # 迁移spectra数据
  if (DBI::dbExistsTable(old_con, "ms1_spectra_data")) {
    old_spectra <- DBI::dbGetQuery(old_con, "SELECT * FROM ms1_spectra_data")
    if (nrow(old_spectra) > 0) {
      DBI::dbWriteTable(new_con, "ms1_spectra_data", old_spectra, append = TRUE, row.names = FALSE)
      cat("  - 迁移了", nrow(old_spectra), "条MS1 spectra记录\n")
    }
  }
  
  # 迁移peaks数据
  if (DBI::dbExistsTable(old_con, "ms1_peaks_data")) {
    old_peaks <- DBI::dbGetQuery(old_con, "SELECT * FROM ms1_peaks_data")
    if (nrow(old_peaks) > 0) {
      DBI::dbWriteTable(new_con, "ms1_peaks_data", old_peaks, append = TRUE, row.names = FALSE)
      cat("  - 迁移了", nrow(old_peaks), "条MS1 peaks记录\n")
    }
  }
}

# 迁移MS2数据
migrate_ms2_data <- function(old_con, new_con) {
  cat("迁移MS2数据...\n")
  
  # 迁移spectra数据
  if (DBI::dbExistsTable(old_con, "ms2_spectra_data")) {
    old_spectra <- DBI::dbGetQuery(old_con, "SELECT * FROM ms2_spectra_data")
    if (nrow(old_spectra) > 0) {
      DBI::dbWriteTable(new_con, "ms2_spectra_data", old_spectra, append = TRUE, row.names = FALSE)
      cat("  - 迁移了", nrow(old_spectra), "条MS2 spectra记录\n")
    }
  }
  
  # 迁移peaks数据
  if (DBI::dbExistsTable(old_con, "ms2_peaks_data")) {
    old_peaks <- DBI::dbGetQuery(old_con, "SELECT * FROM ms2_peaks_data")
    if (nrow(old_peaks) > 0) {
      DBI::dbWriteTable(new_con, "ms2_peaks_data", old_peaks, append = TRUE, row.names = FALSE)
      cat("  - 迁移了", nrow(old_peaks), "条MS2 peaks记录\n")
    }
  }
}

# 主修复函数
repair_database <- function(db_path, create_backup = TRUE) {
  cat("=== 开始数据库修复 ===\n")
  
  # 首先验证数据库
  validation_result <- validate_database_integrity(db_path)
  
  if (validation_result$valid) {
    cat("数据库验证通过，无需修复\n")
    return(list(success = TRUE, message = "数据库正常"))
  }
  
  cat("发现数据库问题，开始修复...\n")
  
  # 修复结构
  repair_result <- repair_database_structure(db_path)
  
  if (repair_result$success) {
    cat("数据库修复成功\n")
    return(list(success = TRUE, backup_path = repair_result$backup_path))
  } else {
    cat("数据库修复失败\n")
    return(repair_result)
  }
}
