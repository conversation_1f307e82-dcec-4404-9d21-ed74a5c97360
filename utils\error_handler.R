# Utils层 - 错误处理
# 提供统一的错误处理和异常管理

# 全局错误处理器
global_error_handler <- function(e) {
  error_msg <- paste("全局错误:", e$message)
  log_error(error_msg)
  
  # 可以在这里添加错误通知、邮件发送等功能
  return(error_msg)
}

# 安全执行函数
safe_execute <- function(func, ...) {
  tryCatch({
    result <- func(...)
    return(list(success = TRUE, result = result, error = NULL))
  }, error = function(e) {
    error_msg <- paste("执行错误:", e$message)
    log_error(error_msg)
    return(list(success = FALSE, result = NULL, error = error_msg))
  }, warning = function(w) {
    warning_msg <- paste("执行警告:", w$message)
    log_warning(warning_msg)
    return(list(success = TRUE, result = func(...), warning = warning_msg))
  })
}

# 验证文件格式 - 使用配置管理器中的支持格式列表
validate_file_format <- function(file_path) {
  config <- load_project_config()
  supported_formats <- config$data$supported_formats
  file_ext <- paste0(".", tolower(tools::file_ext(file_path)))

  if (!file_ext %in% supported_formats) {
    return(list(valid = FALSE, error = paste("不支持的文件格式:", file_ext)))
  }

  if (!file.exists(file_path)) {
    return(list(valid = FALSE, error = "文件不存在"))
  }

  return(list(valid = TRUE, error = NULL))
}

# 验证监控参数
validate_monitor_config <- function(config) {
  errors <- c()
  
  # 检查监控类型
  if (length(config$types) == 0) {
    errors <- c(errors, "至少需要选择一个监控类型")
  }
  
  # 检查监控离子
  if (length(config$ions) == 0) {
    errors <- c(errors, "需要指定监控离子")
  }
  
  # 检查项目信息
  if (is.null(config$project) || config$project == "") {
    errors <- c(errors, "需要指定项目名称")
  }
  
  if (length(errors) > 0) {
    return(list(valid = FALSE, errors = errors))
  }
  
  return(list(valid = TRUE, errors = NULL))
}

# 解析监控离子字符串
parse_monitor_ions <- function(ions_string) {
  if (is.null(ions_string) || ions_string == "") {
    return(numeric(0))
  }
  
  tryCatch({
    # 分割字符串并转换为数值
    ions <- as.numeric(strsplit(ions_string, "[,\\s]+")[[1]])
    ions <- ions[!is.na(ions)]  # 移除NA值
    
    if (length(ions) == 0) {
      log_warning("未解析到有效的监控离子")
      return(numeric(0))
    }
    
    log_info(paste("解析监控离子:", paste(ions, collapse = ", ")))
    return(ions)
    
  }, error = function(e) {
    log_error(paste("解析监控离子失败:", e$message))
    return(numeric(0))
  })
} 