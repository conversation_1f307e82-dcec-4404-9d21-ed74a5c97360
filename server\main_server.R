# Server层 - 主服务器逻辑（重构版本）
# 负责响应式编程、事件处理、业务逻辑协调

server <- function(input, output, session) {
  # 设置文件上传大小限制
  options(shiny.maxRequestSize = 124 * 1024^2)  # 单位字节

  # 响应式变量
  monitoring_status <- reactiveVal(FALSE)
  data_loaded <- reactiveVal(FALSE)

  # 增强的通知函数
  showEnhancedNotification <- function(type, title, message, duration = 5000) {
    session$sendCustomMessage("showEnhancedNotification", list(
      type = type,
      title = title,
      message = message,
      duration = duration
    ))
  }

  # 错误处理包装函数
  safeExecute <- function(expr, error_title = "操作失败", success_title = "操作成功", success_message = NULL) {
    tryCatch({
      result <- expr
      if (!is.null(success_message)) {
        showEnhancedNotification("message", success_title, success_message)
      }
      return(result)
    }, error = function(e) {
      error_msg <- paste("错误详情:", e$message)
      showEnhancedNotification("error", error_title, error_msg, duration = 8000)
      log_error(paste(error_title, ":", e$message))
      return(NULL)
    })
  }

  # 验证函数
  validateInput <- function(value, name, required = TRUE, min_length = 0) {
    if (required && (is.null(value) || value == "")) {
      showEnhancedNotification("warning", "输入验证失败", paste(name, "不能为空"), duration = 3000)
      return(FALSE)
    }
    if (nchar(value) < min_length) {
      showEnhancedNotification("warning", "输入验证失败", paste(name, "长度不能少于", min_length, "个字符"), duration = 3000)
      return(FALSE)
    }
    return(TRUE)
  }

  # 磁盘空间检查函数
  check_disk_space <- function(path) {
    tryCatch({
      # 使用fs包检查磁盘空间
      if (requireNamespace("fs", quietly = TRUE)) {
        info <- fs::file_info(path)
        return(1024^3)  # 返回1GB作为默认可用空间
      } else {
        # 备用方案：使用系统命令
        if (.Platform$OS.type == "windows") {
          # Windows系统
          return(1024^3)  # 返回1GB
        } else {
          # Unix/Linux系统
          result <- system(paste("df", shQuote(path), "| tail -1 | awk '{print $4}'"), intern = TRUE)
          if (length(result) > 0 && !is.na(as.numeric(result))) {
            return(as.numeric(result) * 1024)  # 转换为字节
          }
        }
      }
      return(1024^3)  # 默认返回1GB
    }, error = function(e) {
      return(1024^3)  # 出错时返回1GB
    })
  }
  
  # 路径记忆功能
  path_memory <- reactiveVal(list(
    new_project_path = "",
    import_project_path = "",
    monitor_folder_path = ""
  ))
  
  # 加载路径记忆
  observe({
    tryCatch({
      memory_file <- file.path(GLOBAL_CONFIG$global_config_path, "path_memory.json")
      if (file.exists(memory_file)) {
        memory_data <- jsonlite::fromJSON(readLines(memory_file, warn = FALSE))
        path_memory(memory_data)
      }
    }, error = function(e) {
      log_warning("无法加载路径记忆文件")
    })
  })
  
  # 保存路径记忆
  save_path_memory <- function() {
    tryCatch({
      memory_file <- file.path(GLOBAL_CONFIG$global_config_path, "path_memory.json")
      dir.create(dirname(memory_file), recursive = TRUE, showWarnings = FALSE)
      json_data <- jsonlite::toJSON(path_memory(), auto_unbox = TRUE, pretty = TRUE)
      writeLines(json_data, memory_file, useBytes = TRUE)
    }, error = function(e) {
      log_warning("无法保存路径记忆文件")
    })
  }
  
  # 设置shinyFiles的根目录
  volumes <- c("C:" = "C:/", "D:" = "D:/", "E:" = "E:/", "F:" = "F:/", "G:" = "G:/", 
               "H:" = "H:/", "I:" = "I:/", "J:" = "J:/", "K:" = "K:/", "L:" = "L:/",
               "M:" = "M:/", "N:" = "N:/", "O:" = "O:/", "P:" = "P:/", "Q:" = "Q:/",
               "R:" = "R:/", "S:" = "S:/", "T:" = "T:/", "U:" = "U:/", "V:" = "V:/",
               "W:" = "W:/", "X:" = "X:/", "Y:" = "Y:/", "Z:" = "Z:/")
  
  # 过滤存在的驱动器
  volumes <- volumes[sapply(volumes, dir.exists)]

  # 初始化
  observe({
    log_info("应用启动")
  })

  # 新建项目路径验证（使用文本输入）
  # 不再需要文件夹选择器相关代码

  # 新建项目处理 - 增强版，确保UI立即刷新
  observeEvent(input$new_project_btn, {
    # 输入验证
    if (!validateInput(input$new_project_name, "项目名称", required = TRUE, min_length = 2)) {
      session$sendCustomMessage("setButtonLoading", list(buttonId = "new_project_btn", loading = FALSE))
      return()
    }

    # 验证自定义路径输入
    if (is.null(input$custom_project_path) || input$custom_project_path == "") {
      showEnhancedNotification("warning", "路径输入错误", "请输入项目保存路径", duration = 3000)
      session$sendCustomMessage("setButtonLoading", list(buttonId = "new_project_btn", loading = FALSE))
      return()
    }

    # 显示加载状态
    session$sendCustomMessage("showLoading", list(message = "正在创建项目...", showProgress = FALSE))
    
    safeExecute({
      project_name <- input$new_project_name
      project_description <- input$new_project_description
      custom_path <- input$custom_project_path

      if (is.null(project_description) || project_description == "") {
        project_description <- "新建项目"
      }

      if (is.null(custom_path) || custom_path == "") {
        showEnhancedNotification("warning", "路径验证失败", "请选择有效的项目保存路径", duration = 3000)
        return()
      }

      # 检查项目名称是否已存在
      if (dir.exists(file.path(custom_path, project_name))) {
        showEnhancedNotification("warning", "项目已存在", "该路径下已存在同名项目，请选择其他名称", duration = 5000)
        return()
      }

      # 创建项目，传递自定义路径
      result <- create_project(project_name, project_description, custom_path)
      if (result$success) {
        # 设置项目为活动状态
        if (exists("project_active")) {
          project_active(TRUE)
        }
        if (exists("current_project_info")) {
          current_project_info(result$project)
        }

        showEnhancedNotification("message", "项目创建成功",
                                paste("项目", project_name, "已成功创建，正在进入工作区..."), duration = 3000)
        log_info(paste("项目创建成功:", project_name, "路径:", result$project$path))

        # 清空输入框
        updateTextInput(session, "new_project_name", value = "")
        updateTextAreaInput(session, "new_project_description", value = "")
        updateTextInput(session, "custom_project_path", value = "")

        # 自动进入工作区（延迟执行以确保状态更新）
        session$sendCustomMessage("autoEnterWorkspace", list(delay = 1000))

        # 使用定时器显示工作区加载完成通知
        later::later(function() {
          showEnhancedNotification("info", "进入工作区", "工作区界面已加载", duration = 2000)
        }, delay = 2)

        log_info("项目创建成功后自动进入工作区")

        # 强制刷新UI
        session$sendCustomMessage("refreshUI", list())

        # 延迟刷新项目信息
        invalidateLater(500)
      } else {
        showEnhancedNotification("error", "项目创建失败",
                                result$message %||% "未知错误，请检查路径权限", duration = 8000)
      }
    }, error_title = "项目创建失败")

    # 隐藏加载状态
    session$sendCustomMessage("hideLoading", list())
    session$sendCustomMessage("setButtonLoading", list(buttonId = "new_project_btn", loading = FALSE))
  })

  # 导入项目路径验证（使用文本输入）
  observeEvent(input$validate_import_path, {
    if (is.null(input$import_project_path) || input$import_project_path == "") {
      showEnhancedNotification("warning", "路径输入错误", "请先输入项目文件夹路径", duration = 3000)
      return()
    }

    project_path <- input$import_project_path

    # 检查路径是否存在
    if (!dir.exists(project_path)) {
      session$sendCustomMessage("updateValidationInfo", list(
        show = TRUE,
        type = "error",
        message = "路径不存在或无法访问"
      ))
      session$sendCustomMessage("enableImportButton", FALSE)
      showEnhancedNotification("error", "路径验证失败", "指定的路径不存在或无法访问", duration = 3000)
      return()
    }

    # 检查是否为有效的项目文件夹
    required_dirs <- c("config")
    missing_dirs <- required_dirs[!sapply(required_dirs, function(d) dir.exists(file.path(project_path, d)))]

    if (length(missing_dirs) > 0) {
      session$sendCustomMessage("updateValidationInfo", list(
        show = TRUE,
        type = "error",
        message = paste("缺少必要的文件夹:", paste(missing_dirs, collapse = ", "))
      ))
      session$sendCustomMessage("enableImportButton", FALSE)
      showEnhancedNotification("error", "项目结构无效",
                              paste("缺少必要的文件夹:", paste(missing_dirs, collapse = ", ")), duration = 5000)
      return()
    }

    # 验证成功
    session$sendCustomMessage("updateValidationInfo", list(
      show = TRUE,
      type = "success",
      message = "项目验证成功，可以导入"
    ))
    session$sendCustomMessage("enableImportButton", TRUE)
    showEnhancedNotification("message", "路径验证成功", "项目结构有效，可以导入", duration = 3000)
  })

  # 导入项目处理 - 增强版，确保UI立即刷新
  observeEvent(input$import_project_btn, {
    # 验证导入路径输入
    if (is.null(input$import_project_path) || input$import_project_path == "") {
      showEnhancedNotification("warning", "路径输入错误", "请输入要导入的项目文件夹路径", duration = 3000)
      session$sendCustomMessage("setButtonLoading", list(buttonId = "import_project_btn", loading = FALSE))
      return()
    }

    # 显示加载状态
    session$sendCustomMessage("showLoading", list(message = "正在导入项目...", showProgress = FALSE))
    
    safeExecute({
      project_path <- input$import_project_path

      if (is.null(project_path) || project_path == "") {
        showEnhancedNotification("warning", "路径验证失败", "请选择有效的项目文件夹", duration = 3000)
        return()
      }

      # 检查是否为有效的项目文件夹
      if (!dir.exists(project_path)) {
        showEnhancedNotification("error", "文件夹不存在", "选择的项目文件夹不存在", duration = 5000)
        return()
      }

      # 检查项目文件夹是否包含必要的结构
      required_dirs <- c("config")  # 只检查config目录，data目录可以在导入时创建
      missing_dirs <- required_dirs[!sapply(required_dirs, function(d) dir.exists(file.path(project_path, d)))]

      if (length(missing_dirs) > 0) {
        showEnhancedNotification("error", "项目结构无效",
                                paste("缺少必要的文件夹:", paste(missing_dirs, collapse = ", ")), duration = 5000)
        return()
      }

      # 创建可选的目录结构（如果不存在）
      optional_dirs <- c("data", "results", "reports")
      for (dir_name in optional_dirs) {
        dir_path <- file.path(project_path, dir_name)
        if (!dir.exists(dir_path)) {
          dir.create(dir_path, recursive = TRUE, showWarnings = FALSE)
        }
      }

      # 导入项目
      result <- import_project(project_path)
      if (result$success) {
        # 设置项目为活动状态
        if (exists("project_active")) {
          project_active(TRUE)
        }
        if (exists("current_project_info")) {
          current_project_info(result$project)
        }

        showEnhancedNotification("message", "项目导入成功",
                                paste("项目已成功导入，正在进入工作区..."), duration = 3000)
        log_info(paste("项目导入成功:", project_path))

        # 自动进入工作区（延迟执行以确保状态更新）
        session$sendCustomMessage("autoEnterWorkspace", list(delay = 1000))

        # 使用定时器显示工作区加载完成通知
        later::later(function() {
          showEnhancedNotification("info", "进入工作区", "工作区界面已加载", duration = 2000)
        }, delay = 2)

        log_info("项目导入成功后自动进入工作区")

        # 强制刷新UI
        session$sendCustomMessage("refreshUI", list())

        # 延迟刷新项目信息
        invalidateLater(500)
      } else {
        showEnhancedNotification("error", "项目导入失败",
                                result$message %||% "导入过程中发生未知错误", duration = 8000)
      }
    }, error_title = "项目导入失败")

    # 隐藏加载状态
    session$sendCustomMessage("hideLoading", list())
    session$sendCustomMessage("setButtonLoading", list(buttonId = "import_project_btn", loading = FALSE))
  })

  # 监控离子配置相关变量
  configured_monitor_ions <- reactiveVal(list())
  
  # 导入监控离子YAML配置
  observeEvent(input$monitor_ions_yaml, {
    req(input$monitor_ions_yaml)
    
    tryCatch({
      file_path <- input$monitor_ions_yaml$datapath
      
      # 加载监控离子配置
      success <- monitor_ions_config$load_from_yaml(file_path)
      
      if (success) {
        # 获取配置摘要
        summary <- monitor_ions_config$get_summary()
        
        # 更新已配置的监控离子
        all_ions <- monitor_ions_config$get_all_monitor_ions()
        configured_monitor_ions(all_ions)
        
        showNotification(paste("监控离子配置已导入，包含", summary$total_compounds, "个化合物，", summary$total_monitor_ions, "个监控离子"), type = "message")
        log_info(paste("监控离子配置已导入:", file_path))
      } else {
        showNotification("监控离子配置导入失败", type = "error")
      }
      
    }, error = function(e) {
      showNotification(paste("监控离子配置导入失败:", e$message), type = "error")
      log_error(paste("监控离子配置导入失败:", e$message))
    })
  })
  
  # 预览监控离子配置
  observeEvent(input$preview_monitor_ions, {
    req(configured_monitor_ions())
    
    ions <- configured_monitor_ions()
    if (length(ions) == 0) {
      showNotification("没有配置的监控离子", type = "warning")
      return()
    }
    
    # 创建预览内容
    preview_text <- "监控离子配置预览:\n\n"
    for (i in seq_along(ions)) {
      ion <- ions[[i]]
      preview_text <- paste0(preview_text, 
                            i, ". ", ion$compound_name, "\n",
                            "   质荷比: ", ion$mz, "\n",
                            "   保留时间: ", ion$retention_time, " 分钟\n",
                            "   扫描模式: ", ion$scan_mode, "\n\n")
    }
    
    showModal(modalDialog(
      title = "监控离子配置预览",
      pre(preview_text),
      size = "l",
      footer = modalButton("关闭")
    ))
  })
  
  # 添加监控离子
  observeEvent(input$add_monitor_ion, {
    # 输入验证
    if (!validateInput(input$compound_name, "化合物名称", required = TRUE, min_length = 2)) return()
    if (!validateInput(input$monitor_ions, "监控离子", required = TRUE, min_length = 1)) return()

    if (is.null(input$molecular_weight) || input$molecular_weight <= 0) {
      showEnhancedNotification("warning", "输入验证失败", "分子质量必须大于0", duration = 3000)
      return()
    }

    if (is.null(input$retention_time) || input$retention_time < 0) {
      showEnhancedNotification("warning", "输入验证失败", "保留时间不能为负数", duration = 3000)
      return()
    }
    
    safeExecute({
      # 解析监控离子
      ions_text <- input$monitor_ions
      ions_list <- strsplit(ions_text, ",")[[1]]
      ions_list <- trimws(ions_list)

      # 验证质荷比值
      valid_ions <- list()
      invalid_ions <- character()

      for (i in seq_along(ions_list)) {
        mz_value <- suppressWarnings(as.numeric(ions_list[i]))
        if (!is.na(mz_value) && mz_value > 0) {
          valid_ions[[length(valid_ions) + 1]] <- list(mz = mz_value)
        } else {
          invalid_ions <- c(invalid_ions, ions_list[i])
        }
      }

      if (length(invalid_ions) > 0) {
        showEnhancedNotification("warning", "质荷比格式错误",
                                paste("以下质荷比值无效:", paste(invalid_ions, collapse = ", "),
                                      "。请输入有效的数值。"), duration = 5000)
      }

      if (length(valid_ions) == 0) {
        showEnhancedNotification("error", "监控离子无效", "没有有效的质荷比值，请检查输入格式", duration = 5000)
        return()
      }

      # 检查化合物名称是否已存在
      existing_compounds <- monitor_ions_config$get_compound_names()
      if (input$compound_name %in% existing_compounds) {
        showEnhancedNotification("warning", "化合物已存在",
                                paste("化合物", input$compound_name, "已存在，将覆盖原有配置"), duration = 4000)
      }

      # 创建化合物配置
      compound_config <- list(
        compound_name = input$compound_name,
        molecular_weight = input$molecular_weight,
        scan_mode = input$scan_mode,
        retention_time = input$retention_time,
        monitor_ions = valid_ions
      )

      # 添加到配置中
      monitor_ions_config$add_compound(compound_config)

      # 更新已配置的监控离子
      all_ions <- monitor_ions_config$get_all_monitor_ions()
      configured_monitor_ions(all_ions)

      # 清空输入框
      updateTextInput(session, "compound_name", value = "")
      updateNumericInput(session, "molecular_weight", value = NULL)
      updateNumericInput(session, "retention_time", value = NULL)
      updateTextInput(session, "monitor_ions", value = "")

      success_msg <- paste("成功添加化合物", input$compound_name, "，包含", length(valid_ions), "个监控离子")
      showEnhancedNotification("message", "监控离子已添加", success_msg, duration = 4000)
      log_info(paste("监控离子已添加:", input$compound_name))

    }, error_title = "添加监控离子失败")
  })

  # 加载测试配置
  observeEvent(input$load_test_config, {
    safeExecute({
      # 加载测试YAML配置文件
      yaml_file <- file.path(getwd(), "data", "config", "test.yaml")

      if (!file.exists(yaml_file)) {
        showEnhancedNotification("error", "配置文件不存在",
                                "测试配置文件不存在，请检查data/config/test.yaml", duration = 5000)
        return()
      }

      # 读取YAML配置
      yaml_config <- yaml::read_yaml(yaml_file)

      if (is.null(yaml_config$monitor_ions) || length(yaml_config$monitor_ions) == 0) {
        showEnhancedNotification("warning", "配置文件无效",
                                "YAML配置文件中没有找到监控离子配置", duration = 5000)
        return()
      }

      # 清空现有配置
      monitor_ions_config$clear_all()

      # 导入配置
      imported_count <- 0
      for (compound in yaml_config$monitor_ions) {
        tryCatch({
          # 转换YAML格式到内部格式
          compound_config <- list(
            compound_name = compound$compound_name,
            molecular_weight = compound$molecular_weight,
            scan_mode = compound$scan_mode,
            retention_time = compound$retention_time,
            monitor_ions = lapply(compound$monitor_ions, function(ion) list(mz = ion$mz))
          )

          monitor_ions_config$add_compound(compound_config)
          imported_count <- imported_count + 1

        }, error = function(e) {
          log_error(paste("导入化合物配置失败:", compound$compound_name, "错误:", e$message))
        })
      }

      # 更新已配置的监控离子
      all_ions <- monitor_ions_config$get_all_monitor_ions()
      configured_monitor_ions(all_ions)

      showEnhancedNotification("message", "测试配置加载成功",
                              paste("成功加载", imported_count, "个化合物配置"), duration = 5000)
      log_info(paste("测试配置加载成功，导入", imported_count, "个化合物"))

    }, error_title = "加载测试配置失败")
  })

  # 导入YAML配置
  observeEvent(input$import_yaml_config, {
    # 触发文件选择
    session$sendCustomMessage("triggerFileInput", list(inputId = "monitor_ions_yaml"))
  })

  # 处理YAML文件导入
  observeEvent(input$monitor_ions_yaml, {
    req(input$monitor_ions_yaml)

    safeExecute({
      yaml_file <- input$monitor_ions_yaml$datapath

      if (is.null(yaml_file) || !file.exists(yaml_file)) {
        showEnhancedNotification("error", "文件不存在", "请选择有效的YAML配置文件", duration = 3000)
        return()
      }

      # 读取YAML配置
      yaml_config <- yaml::read_yaml(yaml_file)

      if (is.null(yaml_config$monitor_ions) || length(yaml_config$monitor_ions) == 0) {
        showEnhancedNotification("warning", "配置文件无效",
                                "YAML配置文件中没有找到监控离子配置", duration = 5000)
        return()
      }

      # 导入配置
      imported_count <- 0
      failed_count <- 0

      for (compound in yaml_config$monitor_ions) {
        tryCatch({
          # 转换YAML格式到内部格式
          compound_config <- list(
            compound_name = compound$compound_name,
            molecular_weight = compound$molecular_weight,
            scan_mode = compound$scan_mode,
            retention_time = compound$retention_time,
            monitor_ions = lapply(compound$monitor_ions, function(ion) list(mz = ion$mz))
          )

          monitor_ions_config$add_compound(compound_config)
          imported_count <- imported_count + 1

        }, error = function(e) {
          failed_count <- failed_count + 1
          log_error(paste("导入化合物配置失败:", compound$compound_name, "错误:", e$message))
        })
      }

      # 更新已配置的监控离子
      all_ions <- monitor_ions_config$get_all_monitor_ions()
      configured_monitor_ions(all_ions)

      success_msg <- paste("成功导入", imported_count, "个化合物配置")
      if (failed_count > 0) {
        success_msg <- paste(success_msg, "，", failed_count, "个失败")
      }

      showEnhancedNotification("message", "YAML配置导入完成", success_msg, duration = 5000)
      log_info(paste("YAML配置导入完成，成功:", imported_count, "失败:", failed_count))

    }, error_title = "YAML配置导入失败")
  })

  # 添加新离子
  observeEvent(input$add_new_ion, {
    showModal(modalDialog(
      title = "添加监控离子",
      size = "l",
      div(
        textInput("modal_compound_name", "化合物名称:", placeholder = "例如: 咖啡因"),
        numericInput("modal_molecular_weight", "分子质量:", value = NULL, min = 0, step = 0.0001),
        selectInput("modal_scan_mode", "扫描模式:",
                   choices = c("正离子模式" = "positive", "负离子模式" = "negative"),
                   selected = "positive"),
        numericInput("modal_retention_time", "参考保留时间 (分钟):", value = NULL, min = 0, step = 0.01),
        textInput("modal_monitor_ions", "监控离子 (m/z值，用逗号分隔):", placeholder = "例如: 195.087, 138.055")
      ),
      footer = tagList(
        modalButton("取消"),
        actionButton("confirm_add_ion", "确认添加", class = "btn-primary")
      )
    ))
  })

  # 确认添加离子
  observeEvent(input$confirm_add_ion, {
    # 输入验证
    if (!validateInput(input$modal_compound_name, "化合物名称", required = TRUE, min_length = 2)) return()
    if (!validateInput(input$modal_monitor_ions, "监控离子", required = TRUE, min_length = 1)) return()

    if (is.null(input$modal_molecular_weight) || input$modal_molecular_weight <= 0) {
      showEnhancedNotification("warning", "输入验证失败", "分子质量必须大于0", duration = 3000)
      return()
    }

    if (is.null(input$modal_retention_time) || input$modal_retention_time < 0) {
      showEnhancedNotification("warning", "输入验证失败", "保留时间不能为负数", duration = 3000)
      return()
    }

    safeExecute({
      # 解析监控离子
      ions_text <- input$modal_monitor_ions
      ions_list <- strsplit(ions_text, ",")[[1]]
      ions_list <- trimws(ions_list)

      # 验证质荷比值
      valid_ions <- list()
      invalid_ions <- character()

      for (i in seq_along(ions_list)) {
        mz_value <- suppressWarnings(as.numeric(ions_list[i]))
        if (!is.na(mz_value) && mz_value > 0) {
          valid_ions[[length(valid_ions) + 1]] <- list(mz = mz_value)
        } else {
          invalid_ions <- c(invalid_ions, ions_list[i])
        }
      }

      if (length(invalid_ions) > 0) {
        showEnhancedNotification("warning", "质荷比格式错误",
                                paste("以下质荷比值无效:", paste(invalid_ions, collapse = ", ")), duration = 5000)
      }

      if (length(valid_ions) == 0) {
        showEnhancedNotification("error", "监控离子无效", "没有有效的质荷比值，请检查输入格式", duration = 5000)
        return()
      }

      # 创建化合物配置
      compound_config <- list(
        compound_name = input$modal_compound_name,
        molecular_weight = input$modal_molecular_weight,
        scan_mode = input$modal_scan_mode,
        retention_time = input$modal_retention_time,
        monitor_ions = valid_ions
      )

      # 添加到配置中
      monitor_ions_config$add_compound(compound_config)

      # 更新已配置的监控离子
      all_ions <- monitor_ions_config$get_all_monitor_ions()
      configured_monitor_ions(all_ions)

      success_msg <- paste("成功添加化合物", input$modal_compound_name, "，包含", length(valid_ions), "个监控离子")
      showEnhancedNotification("message", "监控离子已添加", success_msg, duration = 4000)
      log_info(paste("监控离子已添加:", input$modal_compound_name))

      # 关闭模态框
      removeModal()

    }, error_title = "添加监控离子失败")
  })

  # 导出离子配置
  output$export_ions_config <- downloadHandler(
    filename = function() {
      paste0("monitor_ions_config_", format(Sys.time(), "%Y%m%d_%H%M%S"), ".yaml")
    },
    content = function(file) {
      tryCatch({
        compounds <- monitor_ions_config$get_all_compounds()

        if (length(compounds) == 0) {
          stop("当前没有配置的监控离子")
        }

        # 构建YAML格式的配置
        yaml_config <- list(
          monitor_ions = compounds,
          monitoring_settings = list(
            monitor_types = c("tic", "bpc", "mz", "eic", "rsd"),
            qc_thresholds = list(
              tic_rsd_limit = 15.0,
              bpc_rsd_limit = 20.0,
              mz_tolerance = 0.01,
              retention_time_window = 0.5
            )
          ),
          project_info = list(
            name = get_current_project(),
            description = "导出的监控离子配置",
            version = "1.0.0",
            created_date = format(Sys.Date(), "%Y-%m-%d")
          )
        )

        # 写入文件
        yaml::write_yaml(yaml_config, file)
        log_info(paste("监控离子配置已导出:", file))

      }, error = function(e) {
        log_error(paste("导出监控离子配置失败:", e$message))
        stop(paste("导出失败:", e$message))
      })
    },
    contentType = "application/x-yaml"
  )
  
  # 监控离子表格显示
  output$monitor_ions_table <- DT::renderDataTable({
    # 添加响应式依赖，确保配置更新后刷新表格
    configured_monitor_ions()

    tryCatch({
      # 获取所有配置的化合物
      compounds <- monitor_ions_config$get_all_compounds()

      if (length(compounds) == 0) {
        return(data.frame(
          化合物名称 = character(),
          分子质量 = numeric(),
          扫描模式 = character(),
          保留时间 = numeric(),
          监控离子 = character(),
          stringsAsFactors = FALSE
        ))
      }

      # 构建表格数据
      table_data <- data.frame()
      for (compound in compounds) {
        ions_text <- paste(sapply(compound$monitor_ions, function(x) x$mz), collapse = ", ")

        row_data <- data.frame(
          化合物名称 = compound$compound_name,
          分子质量 = compound$molecular_weight,
          扫描模式 = compound$scan_mode,
          保留时间 = compound$retention_time,
          监控离子 = ions_text,
          stringsAsFactors = FALSE
        )

        table_data <- rbind(table_data, row_data)
      }

      return(table_data)

    }, error = function(e) {
      return(data.frame(
        化合物名称 = "加载失败",
        分子质量 = 0,
        扫描模式 = "",
        保留时间 = 0,
        监控离子 = "",
        stringsAsFactors = FALSE
      ))
    })
  }, options = list(
    pageLength = 10,
    searching = TRUE,
    ordering = TRUE,
    info = TRUE,
    scrollX = TRUE,
    columnDefs = list(
      list(className = "dt-center", targets = c(1, 2, 3)),
      list(className = "dt-left", targets = c(0, 4))
    ),
    language = list(
      search = "搜索:",
      lengthMenu = "显示 _MENU_ 条记录",
      info = "显示第 _START_ 至 _END_ 项结果，共 _TOTAL_ 项",
      paginate = list(previous = "上一页", `next` = "下一页")
    )
  ), selection = 'multiple')

  # 数据文件夹选择处理
  shinyFiles::shinyDirChoose(input, "data_folder_btn", roots = volumes)

  # 显示选择的数据文件夹路径
  output$data_folder_path <- renderText({
    req(input$data_folder_btn)
    if (is.integer(input$data_folder_btn)) {
      return("未选择数据文件夹")
    } else {
      path <- shinyFiles::parseDirPath(volumes, input$data_folder_btn)
      if (!is.null(path) && path != "") {
        # 更新路径记忆
        memory <- path_memory()
        memory$data_folder_path <- path
        path_memory(memory)
        save_path_memory()
      }
      return(path)
    }
  })

  # CSV文件路径显示
  output$csv_file_path <- renderText({
    if (is.null(input$csv_file_input) || is.null(input$csv_file_input$datapath)) {
      return("未选择CSV文件")
    } else {
      return(paste("已选择:", input$csv_file_input$name))
    }
  })




  
  # 项目信息显示 - 使用新的路径管理器
  output$project_info <- renderText({
    tryCatch({
      # 使用路径管理器获取项目信息
      project_root <- get_project_root_path()

      if (is.null(project_root)) {
        return(paste(
          "当前没有活动项目",
          "请先创建或导入项目",
          paste("当前工作目录:", getwd()),
          paste("软件安装路径:", get_app_root_path()),
          sep = "\n"
        ))
      }

      # 尝试读取项目配置文件
      config_file <- get_project_config_path()
      project_info <- NULL

      if (file.exists(config_file)) {
        tryCatch({
          config <- jsonlite::fromJSON(config_file)
          project_info <- list(
            name = config$name %||% "未知",
            path = config$path %||% project_root,
            created_time = config$created_time %||% "未知",
            description = config$description %||% "无描述",
            status = config$status %||% "未知"
          )
        }, error = function(e) {
          log_error(paste("读取项目配置失败:", e$message))
        })
      }

      # 如果没有读取到配置，尝试其他配置文件
      if (is.null(project_info)) {
        config_dir <- get_project_relative_path("config")
        if (dir.exists(config_dir)) {
          json_files <- list.files(config_dir, pattern = "\\.json$", full.names = TRUE)
          for (json_file in json_files) {
            tryCatch({
              config <- jsonlite::fromJSON(json_file)
              if (!is.null(config$name)) {
                project_info <- list(
                  name = config$name,
                  path = config$path %||% project_root,
                  created_time = config$created_time %||% "未知",
                  description = config$description %||% "无描述",
                  status = config$status %||% "未知"
                )
                break
              }
            }, error = function(e) {})
          }
        }
      }

      # 获取项目统计信息
      data_count <- 0
      result_count <- 0
      report_count <- 0

      data_file <- get_data_list_path()
      if (file.exists(data_file)) {
        tryCatch({
          data_list <- read.csv(data_file, stringsAsFactors = FALSE)
          data_count <- nrow(data_list)
        }, error = function(e) {})
      }

      results_dir <- get_project_relative_path("results")
      if (dir.exists(results_dir)) {
        result_count <- length(list.files(results_dir))
      }

      reports_dir <- get_project_relative_path("reports")
      if (dir.exists(reports_dir)) {
        report_count <- length(list.files(reports_dir))
      }

      # 显示项目信息
      if (!is.null(project_info)) {
        info_text <- paste(
          paste("项目名称:", project_info$name),
          paste("项目路径:", project_info$path),
          paste("创建时间:", project_info$created_time),
          paste("项目描述:", project_info$description),
          paste("项目状态:", project_info$status),
          paste("数据文件:", data_count, "个"),
          paste("结果文件:", result_count, "个"),
          paste("报告文件:", report_count, "个"),
          paste("当前工作目录:", getwd()),
          paste("项目根路径:", project_root),
          paste("配置文件:", config_file),
          paste("系统时间:", format(Sys.time(), "%Y-%m-%d %H:%M:%S")),
          paste("应用版本:", GLOBAL_CONFIG$version),
          sep = "\n"
        )
        return(info_text)
      } else {
        return(paste(
          "项目配置文件读取失败",
          paste("项目根路径:", project_root),
          paste("当前工作目录:", getwd()),
          paste("配置文件路径:", config_file),
          paste("配置文件存在:", file.exists(config_file)),
          paste("系统时间:", format(Sys.time(), "%Y-%m-%d %H:%M:%S")),
          sep = "\n"
        ))
      }
    }, error = function(e) {
      return(paste("获取项目信息失败:", e$message, "\n当前工作目录:", getwd()))
    })
  })
  
  # 添加项目信息自动刷新
  observe({
    # 每10秒自动刷新项目信息
    invalidateLater(10000)
  })

  # 读取文件夹数据
  observeEvent(input$read_folder_data, {
    req(input$data_folder_btn)

    if (is.integer(input$data_folder_btn)) {
      showEnhancedNotification("warning", "路径选择错误", "请先选择数据文件夹", duration = 3000)
      return()
    }

    # 显示加载状态
    session$sendCustomMessage("showLoading", list(message = "正在扫描文件夹...", showProgress = TRUE))
    session$sendCustomMessage("updateProgress", list(percent = 10))

    safeExecute({
      folder_path <- shinyFiles::parseDirPath(volumes, input$data_folder_btn)

      if (is.null(folder_path) || folder_path == "" || !dir.exists(folder_path)) {
        showEnhancedNotification("error", "文件夹不存在", "选择的文件夹不存在或无法访问", duration = 5000)
        # 隐藏加载状态
        session$sendCustomMessage("hideLoading", list())
        return()
      }

      # 根据文件类型过滤扫描文件
      pattern <- switch(input$file_pattern,
                       "*.raw" = "\\.raw$",
                       "*.mzML" = "\\.mzML$",
                       "*.mzXML" = "\\.mzXML$",
                       "*.mgf" = "\\.mgf$",
                       "*" = "\\.(raw|mzML|mzXML|mgf)$")

      # 扫描文件
      files <- list.files(folder_path, pattern = pattern, ignore.case = TRUE,
                         recursive = input$include_subfolders, full.names = TRUE)

      session$sendCustomMessage("updateProgress", list(percent = 50))

      if (length(files) == 0) {
        showEnhancedNotification("warning", "未找到文件",
                                paste("在指定文件夹中未找到", input$file_pattern, "格式的文件"),
                                duration = 5000)
        # 隐藏加载状态
        session$sendCustomMessage("hideLoading", list())
        return()
      }

      # 处理找到的文件
      result <- process_folder_files(files, basename(getwd()))

      session$sendCustomMessage("updateProgress", list(percent = 90))

      if (result$success) {
        data_loaded(TRUE)
        session$sendCustomMessage("updateProgress", list(percent = 100))

        showEnhancedNotification("message", "文件夹数据读取完成",
                                paste("成功读取", length(files), "个文件"), duration = 5000)
        log_info(paste("文件夹数据读取成功:", folder_path))

        # 触发数据列表更新
        invalidateLater(100)
      } else {
        showEnhancedNotification("error", "数据处理失败",
                                result$message %||% "处理过程中发生未知错误", duration = 8000)
      }

      # 隐藏加载状态
      Sys.sleep(0.5)
      session$sendCustomMessage("hideLoading", list())
    }, error_title = "文件夹数据读取失败")

    # 确保隐藏加载状态
    session$sendCustomMessage("hideLoading", list())
  })



  # 读取CSV数据
  observeEvent(input$read_csv_data, {
    if (is.null(input$csv_file_input) || is.null(input$csv_file_input$datapath)) {
      showEnhancedNotification("warning", "文件选择错误", "请先选择CSV文件", duration = 3000)
      return()
    }

    # 显示加载状态
    session$sendCustomMessage("showLoading", list(message = "正在读取CSV文件...", showProgress = TRUE))
    session$sendCustomMessage("updateProgress", list(percent = 10))

    safeExecute({
      csv_file <- input$csv_file_input$datapath

      if (!file.exists(csv_file)) {
        showEnhancedNotification("error", "文件不存在", "选择的CSV文件不存在或无法访问", duration = 5000)
        return()
      }

      session$sendCustomMessage("updateProgress", list(percent = 30))

      # 处理CSV文件
      result <- process_csv_file(csv_file, get_current_project())

      session$sendCustomMessage("updateProgress", list(percent = 90))

      if (result$success) {
        data_loaded(TRUE)
        session$sendCustomMessage("updateProgress", list(percent = 100))

        showEnhancedNotification("message", "CSV数据读取完成",
                                paste("成功读取", result$count, "条数据记录"), duration = 5000)
        log_info(paste("CSV文件读取成功:", input$csv_file_input$name))

        # 触发数据列表更新
        invalidateLater(100)
      } else {
        showEnhancedNotification("error", "CSV处理失败",
                                result$message %||% "处理过程中发生未知错误", duration = 8000)
      }

      # 隐藏加载状态
      Sys.sleep(0.5)
      session$sendCustomMessage("hideLoading", list())

    }, error_title = "CSV文件读取失败")
  })



  # 开始监控处理 - 简化版本，根据监控类型判断是否需要监控离子
  observeEvent(input$start_monitor, {
    req(input$monitor_types)

    # 检查是否需要监控离子
    monitor_types <- input$monitor_types
    needs_ions <- any(c("mz", "rsd", "eic") %in% monitor_types)

    if (needs_ions) {
      # 检查是否有配置的监控离子
      ions <- configured_monitor_ions()
      if (length(ions) == 0) {
        showEnhancedNotification("warning", "监控离子配置缺失",
                                "选择的监控类型需要监控离子配置，请先配置监控离子", duration = 5000)
        session$sendCustomMessage("setButtonLoading", list(buttonId = "start_monitor", loading = FALSE))
        return()
      }
    }

    # 显示加载状态
    session$sendCustomMessage("showLoading", list(message = "正在启动监控任务...", showProgress = FALSE))
    
    tryCatch({
      # 获取监控参数
      monitor_config <- list(
        types = monitor_types,
        ions = if (needs_ions) configured_monitor_ions() else list(),
        project = get_current_project(),
        global_settings = monitor_ions_config$global_settings,
        instrument_settings = monitor_ions_config$instrument_settings
      )
      
      # 启动监控任务
      result <- start_monitoring_tasks(monitor_config)
      if (result$success) {
        monitoring_status(TRUE)
        showNotification(result$message, type = "message")
        log_info("监控任务启动成功")
      } else {
        showNotification(result$message, type = "error")
        log_error("监控任务启动失败")
      }

      # 隐藏加载状态
      session$sendCustomMessage("hideLoading", list())
      session$sendCustomMessage("setButtonLoading", list(buttonId = "start_monitor", loading = FALSE))
    }, error = function(e) {
      showNotification(paste("启动监控失败:", e$message), type = "error")
      log_error(paste("启动监控失败:", e$message))

      # 隐藏加载状态
      session$sendCustomMessage("hideLoading", list())
      session$sendCustomMessage("setButtonLoading", list(buttonId = "start_monitor", loading = FALSE))
    })
  })

  # 停止监控处理
  observeEvent(input$stop_monitor, {
    # 显示加载状态
    session$sendCustomMessage("showLoading", list(message = "正在停止监控任务...", showProgress = FALSE))

    tryCatch({
      result <- stop_monitoring_tasks()
      if (result$success) {
        monitoring_status(FALSE)
        showNotification(result$message, type = "default")
        log_info("监控任务停止成功")
      } else {
        showNotification(result$message, type = "error")
        log_error("监控任务停止失败")
      }

      # 隐藏加载状态
      session$sendCustomMessage("hideLoading", list())
      session$sendCustomMessage("setButtonLoading", list(buttonId = "stop_monitor", loading = FALSE))
    }, error = function(e) {
      showNotification(paste("停止监控失败:", e$message), type = "error")
      log_error(paste("停止监控失败:", e$message))

      # 隐藏加载状态
      session$sendCustomMessage("hideLoading", list())
      session$sendCustomMessage("setButtonLoading", list(buttonId = "stop_monitor", loading = FALSE))
    })
  })

  # 导出报告处理
  observeEvent(input$export_report, {
    # 显示加载状态
    session$sendCustomMessage("showLoading", list(message = "正在生成和导出报告...", showProgress = FALSE))

    tryCatch({
      result <- generate_and_export_report(basename(getwd()))
      if (result$success) {
        showNotification(paste("报告导出成功:", result$file), type = "message")
        log_info("报告导出成功")
      } else {
        showNotification(result$message, type = "error")
        log_error("报告导出失败")
      }

      # 隐藏加载状态
      session$sendCustomMessage("hideLoading", list())
      session$sendCustomMessage("setButtonLoading", list(buttonId = "export_report", loading = FALSE))
    }, error = function(e) {
      showNotification(paste("报告导出失败:", e$message), type = "error")
      log_error(paste("报告导出失败:", e$message))

      # 隐藏加载状态
      session$sendCustomMessage("hideLoading", list())
      session$sendCustomMessage("setButtonLoading", list(buttonId = "export_report", loading = FALSE))
    })
  })

  # 数据统计输出
  output$total_files_count <- renderText({
    tryCatch({
      data_list <- get_data_list(basename(getwd()))
      if (is.null(data_list) || nrow(data_list) == 0) return("0")
      return(as.character(nrow(data_list)))
    }, error = function(e) return("0"))
  })

  output$processed_files_count <- renderText({
    tryCatch({
      data_list <- get_data_list(basename(getwd()))
      if (is.null(data_list) || nrow(data_list) == 0) return("0")
      processed_count <- sum(data_list$状态 == "已处理", na.rm = TRUE)
      return(as.character(processed_count))
    }, error = function(e) return("0"))
  })

  output$processing_files_count <- renderText({
    tryCatch({
      data_list <- get_data_list(basename(getwd()))
      if (is.null(data_list) || nrow(data_list) == 0) return("0")
      processing_count <- sum(data_list$状态 == "处理中", na.rm = TRUE)
      return(as.character(processing_count))
    }, error = function(e) return("0"))
  })

  output$total_files_size <- renderText({
    tryCatch({
      data_list <- get_data_list(basename(getwd()))
      if (is.null(data_list) || nrow(data_list) == 0) return("0 MB")
      # 假设大小列包含数值，计算总大小
      total_size <- sum(as.numeric(gsub("[^0-9.]", "", data_list$大小)), na.rm = TRUE)
      if (total_size > 1024) {
        return(paste(round(total_size / 1024, 1), "GB"))
      } else {
        return(paste(round(total_size, 1), "MB"))
      }
    }, error = function(e) return("0 MB"))
  })

  # 数据表格输出已移至 data_management_server.R

  # 结果统计输出
  output$total_results_count <- renderText({
    tryCatch({
      results <- get_monitoring_results(basename(getwd()))
      if (is.null(results) || nrow(results) == 0) return("0")
      return(as.character(nrow(results)))
    }, error = function(e) return("0"))
  })

  output$anomaly_results_count <- renderText({
    tryCatch({
      results <- get_monitoring_results(basename(getwd()))
      if (is.null(results) || nrow(results) == 0) return("0")
      anomaly_count <- sum(results$状态 == "异常", na.rm = TRUE)
      return(as.character(anomaly_count))
    }, error = function(e) return("0"))
  })

  output$last_update_time <- renderText({
    tryCatch({
      results <- get_monitoring_results(basename(getwd()))
      if (is.null(results) || nrow(results) == 0) return("无数据")
      # 获取最新的时间戳
      latest_time <- max(as.POSIXct(results$时间, format = "%H:%M:%S"), na.rm = TRUE)
      return(format(latest_time, "%H:%M:%S"))
    }, error = function(e) return("无数据"))
  })

  # 结果表格输出 - 增强版本，支持筛选
  output$results_table <- DT::renderDataTable({
    tryCatch({
      results <- get_monitoring_results(basename(getwd()))
      if (is.null(results) || nrow(results) == 0) {
        return(data.frame(时间 = character(), 类型 = character(), 值 = numeric(), 状态 = character()))
      }

      # 根据筛选条件过滤数据
      if (!is.null(input$results_filter_type) && input$results_filter_type != "all") {
        filter_type <- switch(input$results_filter_type,
                             "tic" = "TIC",
                             "bpc" = "BPC",
                             "eic" = "EIC",
                             "mz" = "m/z",
                             "rsd" = "RSD",
                             input$results_filter_type)
        results <- results[results$类型 == filter_type, ]
      }

      return(results)
    }, error = function(e) {
      return(data.frame(时间 = "加载失败", 类型 = "错误", 值 = 0, 状态 = "错误"))
    })
  }, options = list(
    pageLength = 20,
    searching = TRUE,
    ordering = TRUE,
    info = TRUE,
    scrollX = TRUE,
    columnDefs = list(
      list(targets = c(1), searchable = TRUE),  # 类型可搜索
      list(targets = c(3), searchable = TRUE),  # 状态可搜索
      list(className = "dt-center", targets = c(0, 1, 3)),  # 居中对齐
      list(className = "dt-right", targets = c(2))  # 数值右对齐
    ),
    language = list(
      search = "搜索:",
      lengthMenu = "显示 _MENU_ 条记录",
      info = "显示第 _START_ 至 _END_ 项结果，共 _TOTAL_ 项",
      paginate = list(previous = "上一页", `next` = "下一页")
    )
  ))

  # 统计报告输出
  output$stats_report <- renderText({
    tryCatch({
      stats <- generate_statistics_report(basename(getwd()))
      return(stats)
    }, error = function(e) {
      return("无法生成统计报告")
    })
  })

  # 监控状态输出
  output$monitoring_status_text <- renderText({
    status <- monitoring_status()
    if (status) {
      return("运行中")
    } else {
      return("已停止")
    }
  })

  output$total_data_points <- renderText({
    tryCatch({
      results <- get_monitoring_results(basename(getwd()))
      if (is.null(results) || nrow(results) == 0) return("0")
      return(as.character(nrow(results)))
    }, error = function(e) return("0"))
  })

  output$anomaly_detection_count <- renderText({
    tryCatch({
      results <- get_monitoring_results(basename(getwd()))
      if (is.null(results) || nrow(results) == 0) return("0")
      anomaly_count <- sum(results$状态 == "异常", na.rm = TRUE)
      return(as.character(anomaly_count))
    }, error = function(e) return("0"))
  })

  output$chart_last_update <- renderText({
    return(format(Sys.time(), "%H:%M:%S"))
  })

  # 图表输出 - 增强版本
  output$tic_plot <- renderPlotly({
    tryCatch({
      plot_data <- get_plot_data(basename(getwd()), "tic")

      # 根据时间范围筛选数据
      if (!is.null(input$chart_time_range) && input$chart_time_range != "all") {
        hours_back <- switch(input$chart_time_range,
                           "1h" = 1,
                           "6h" = 6,
                           "24h" = 24,
                           1)
        cutoff_time <- Sys.time() - hours_back * 3600
        plot_data <- plot_data[as.POSIXct(plot_data$time, format = "%H:%M:%S") >= cutoff_time, ]
      }

      if (nrow(plot_data) == 0) {
        return(plot_ly() %>%
               layout(title = list(text = "TIC监控 - 无数据", font = list(size = 14)),
                      xaxis = list(title = "时间"),
                      yaxis = list(title = "强度"),
                      plot_bgcolor = 'rgba(0,0,0,0)',
                      paper_bgcolor = 'rgba(0,0,0,0)'))
      }

      plot_ly(plot_data, x = ~time, y = ~value, type = 'scatter', mode = 'lines+markers',
              line = list(color = '#667eea', width = 2),
              marker = list(size = 4, color = '#667eea'),
              hovertemplate = '<b>时间:</b> %{x}<br><b>强度:</b> %{y}<extra></extra>') %>%
        layout(title = list(text = "", font = list(size = 14)),
               xaxis = list(title = "时间", showgrid = TRUE, gridcolor = 'rgba(128,128,128,0.2)'),
               yaxis = list(title = "强度", showgrid = TRUE, gridcolor = 'rgba(128,128,128,0.2)'),
               plot_bgcolor = 'rgba(0,0,0,0)',
               paper_bgcolor = 'rgba(0,0,0,0)',
               showlegend = FALSE) %>%
        config(displayModeBar = TRUE, displaylogo = FALSE,
               modeBarButtonsToRemove = c('pan2d', 'select2d', 'lasso2d', 'autoScale2d', 'hoverClosestCartesian', 'hoverCompareCartesian'))
    }, error = function(e) {
      plot_ly() %>% layout(title = list(text = "TIC监控 - 加载失败", font = list(size = 14)),
                          plot_bgcolor = 'rgba(0,0,0,0)',
                          paper_bgcolor = 'rgba(0,0,0,0)')
    })
  })

  output$bpc_plot <- renderPlotly({
    tryCatch({
      plot_data <- get_plot_data(basename(getwd()), "bpc")

      # 根据时间范围筛选数据
      if (!is.null(input$chart_time_range) && input$chart_time_range != "all") {
        hours_back <- switch(input$chart_time_range,
                           "1h" = 1,
                           "6h" = 6,
                           "24h" = 24,
                           1)
        cutoff_time <- Sys.time() - hours_back * 3600
        plot_data <- plot_data[as.POSIXct(plot_data$time, format = "%H:%M:%S") >= cutoff_time, ]
      }

      if (nrow(plot_data) == 0) {
        return(plot_ly() %>%
               layout(title = list(text = "BPC监控 - 无数据", font = list(size = 14)),
                      xaxis = list(title = "时间"),
                      yaxis = list(title = "强度"),
                      plot_bgcolor = 'rgba(0,0,0,0)',
                      paper_bgcolor = 'rgba(0,0,0,0)'))
      }

      plot_ly(plot_data, x = ~time, y = ~value, type = 'scatter', mode = 'lines+markers',
              line = list(color = '#28a745', width = 2),
              marker = list(size = 4, color = '#28a745'),
              hovertemplate = '<b>时间:</b> %{x}<br><b>强度:</b> %{y}<extra></extra>') %>%
        layout(title = list(text = "", font = list(size = 14)),
               xaxis = list(title = "时间", showgrid = TRUE, gridcolor = 'rgba(128,128,128,0.2)'),
               yaxis = list(title = "强度", showgrid = TRUE, gridcolor = 'rgba(128,128,128,0.2)'),
               plot_bgcolor = 'rgba(0,0,0,0)',
               paper_bgcolor = 'rgba(0,0,0,0)',
               showlegend = FALSE) %>%
        config(displayModeBar = TRUE, displaylogo = FALSE,
               modeBarButtonsToRemove = c('pan2d', 'select2d', 'lasso2d', 'autoScale2d', 'hoverClosestCartesian', 'hoverCompareCartesian'))
    }, error = function(e) {
      plot_ly() %>% layout(title = list(text = "BPC监控 - 加载失败", font = list(size = 14)),
                          plot_bgcolor = 'rgba(0,0,0,0)',
                          paper_bgcolor = 'rgba(0,0,0,0)')
    })
  })

  output$mz_plot <- renderPlotly({
    tryCatch({
      plot_data <- get_plot_data(basename(getwd()), "mz")

      # 根据时间范围筛选数据
      if (!is.null(input$chart_time_range) && input$chart_time_range != "all") {
        hours_back <- switch(input$chart_time_range,
                           "1h" = 1,
                           "6h" = 6,
                           "24h" = 24,
                           1)
        cutoff_time <- Sys.time() - hours_back * 3600
        plot_data <- plot_data[as.POSIXct(plot_data$time, format = "%H:%M:%S") >= cutoff_time, ]
      }

      if (nrow(plot_data) == 0) {
        return(plot_ly() %>%
               layout(title = list(text = "m/z监控 - 无数据", font = list(size = 14)),
                      xaxis = list(title = "时间"),
                      yaxis = list(title = "m/z值"),
                      plot_bgcolor = 'rgba(0,0,0,0)',
                      paper_bgcolor = 'rgba(0,0,0,0)'))
      }

      plot_ly(plot_data, x = ~time, y = ~value, type = 'scatter', mode = 'lines+markers',
              line = list(color = '#ffc107', width = 2),
              marker = list(size = 4, color = '#ffc107'),
              hovertemplate = '<b>时间:</b> %{x}<br><b>m/z:</b> %{y}<extra></extra>') %>%
        layout(title = list(text = "", font = list(size = 14)),
               xaxis = list(title = "时间", showgrid = TRUE, gridcolor = 'rgba(128,128,128,0.2)'),
               yaxis = list(title = "m/z值", showgrid = TRUE, gridcolor = 'rgba(128,128,128,0.2)'),
               plot_bgcolor = 'rgba(0,0,0,0)',
               paper_bgcolor = 'rgba(0,0,0,0)',
               showlegend = FALSE) %>%
        config(displayModeBar = TRUE, displaylogo = FALSE,
               modeBarButtonsToRemove = c('pan2d', 'select2d', 'lasso2d', 'autoScale2d', 'hoverClosestCartesian', 'hoverCompareCartesian'))
    }, error = function(e) {
      plot_ly() %>% layout(title = list(text = "m/z监控 - 加载失败", font = list(size = 14)),
                          plot_bgcolor = 'rgba(0,0,0,0)',
                          paper_bgcolor = 'rgba(0,0,0,0)')
    })
  })

  output$eic_plot <- renderPlotly({
    tryCatch({
      plot_data <- get_plot_data(basename(getwd()), "eic")

      # 根据时间范围筛选数据
      if (!is.null(input$chart_time_range) && input$chart_time_range != "all") {
        hours_back <- switch(input$chart_time_range,
                           "1h" = 1,
                           "6h" = 6,
                           "24h" = 24,
                           1)
        cutoff_time <- Sys.time() - hours_back * 3600
        plot_data <- plot_data[as.POSIXct(plot_data$time, format = "%H:%M:%S") >= cutoff_time, ]
      }

      if (nrow(plot_data) == 0) {
        return(plot_ly() %>%
               layout(title = list(text = "EIC监控 - 无数据", font = list(size = 14)),
                      xaxis = list(title = "时间"),
                      yaxis = list(title = "强度"),
                      plot_bgcolor = 'rgba(0,0,0,0)',
                      paper_bgcolor = 'rgba(0,0,0,0)'))
      }

      plot_ly(plot_data, x = ~time, y = ~value, type = 'scatter', mode = 'lines+markers',
              line = list(color = '#dc3545', width = 2),
              marker = list(size = 4, color = '#dc3545'),
              hovertemplate = '<b>时间:</b> %{x}<br><b>强度:</b> %{y}<extra></extra>') %>%
        layout(title = list(text = "", font = list(size = 14)),
               xaxis = list(title = "时间", showgrid = TRUE, gridcolor = 'rgba(128,128,128,0.2)'),
               yaxis = list(title = "强度", showgrid = TRUE, gridcolor = 'rgba(128,128,128,0.2)'),
               plot_bgcolor = 'rgba(0,0,0,0)',
               paper_bgcolor = 'rgba(0,0,0,0)',
               showlegend = FALSE) %>%
        config(displayModeBar = TRUE, displaylogo = FALSE,
               modeBarButtonsToRemove = c('pan2d', 'select2d', 'lasso2d', 'autoScale2d', 'hoverClosestCartesian', 'hoverCompareCartesian'))
    }, error = function(e) {
      plot_ly() %>% layout(title = list(text = "EIC监控 - 加载失败", font = list(size = 14)),
                          plot_bgcolor = 'rgba(0,0,0,0)',
                          paper_bgcolor = 'rgba(0,0,0,0)')
    })
  })

  # 数据管理相关代码已移至 data_management_server.R

  # 预览CSV数据
  observeEvent(input$preview_csv_data, {
    req(input$csv_config)
    tryCatch({
      # 预览CSV数据逻辑
      showNotification("CSV数据预览功能开发中", type = "default")
    }, error = function(e) {
      showNotification(paste("预览失败:", e$message), type = "error")
    })
  })

  # 监控参数相关逻辑

  # 在所有初始化完成后加载服务器模块
  # 首先确保路径管理器已初始化
  if (exists("init_path_manager")) {
    init_path_manager()
  }

  # 使用安全的source函数，指定环境为当前环境，让模块能够访问input、output、session
  safe_source("server/project_selection_server.R", encoding = "UTF-8", local = environment())
  safe_source("server/workspace_server.R", encoding = "UTF-8", local = environment())
  safe_source("server/data_management_server.R", encoding = "UTF-8", local = environment())

  # 加载同位素内标监控服务器逻辑
  tryCatch({
    source("server/isotope_monitoring_server.R", encoding = "UTF-8")
    source("ui/isotope_monitoring_ui.R", encoding = "UTF-8")
    # 调用同位素监控服务器函数
    isotope_monitoring_server(input, output, session)
    log_info("同位素内标监控模块已加载")
  }, error = function(e) {
    log_error(paste("加载同位素内标监控模块失败:", e$message))
  })

  # 在server模块加载完成后，执行项目状态恢复
  # 使用响应式变量来确保只执行一次
  project_restored <- reactiveVal(FALSE)

  observe({
    # 只在未恢复状态时执行
    if (!project_restored()) {
      # 延迟执行，确保所有响应式变量都已初始化
      invalidateLater(100)

      tryCatch({
        project_root <- get_project_root_path()
        if (!is.null(project_root) && dir.exists(project_root)) {
          # 检查项目配置文件是否存在
          config_file <- get_project_config_path()
          if (file.exists(config_file)) {
            # 读取项目配置
            config <- jsonlite::fromJSON(config_file)

            # 恢复项目状态
            if (exists("project_active")) {
              project_active(TRUE)
            }
            if (exists("current_project_info")) {
              current_project_info(config)
            }

            # 自动进入工作区
            session$sendCustomMessage("autoEnterWorkspace", list(delay = 500))

            log_info(paste("恢复活动项目:", config$name %||% "未知项目"))
            showEnhancedNotification("info", "项目已恢复",
                                    paste("已自动恢复项目:", config$name %||% "未知项目"), duration = 3000)

            # 标记为已恢复
            project_restored(TRUE)
          }
        }
      }, error = function(e) {
        log_warning(paste("项目状态恢复失败:", e$message))
        project_restored(TRUE)  # 即使失败也标记为已尝试，避免重复执行
      })
    }
  })
}