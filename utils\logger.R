# Utils层 - 日志系统
# 提供统一的日志记录功能

# 全局日志文件路径
LOG_FILE <- NULL

# 初始化日志系统（简化版）
init_logger <- function(log_file_path = NULL) {
  # 简化版不需要文件路径
  log_info("日志系统初始化完成（控制台模式）")
}

# 写入日志的通用函数（简化版，只输出到控制台）
write_log <- function(level, message) {
  timestamp <- format(Sys.time(), "%Y-%m-%d %H:%M:%S")
  log_entry <- paste0("[", timestamp, "] [", level, "] ", message)

  # 只输出到控制台，不写文件
  cat(log_entry, "\n")
}

# 不同级别的日志函数
log_info <- function(message) {
  write_log("INFO", message)
}

log_warning <- function(message) {
  write_log("WARNING", message)
}

log_error <- function(message) {
  write_log("ERROR", message)
}

log_debug <- function(message) {
  write_log("DEBUG", message)
}

# 自动初始化日志系统（简化版）
init_logger()