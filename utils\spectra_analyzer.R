# Spectra数据结构分析器
# 用于分析现有_spectra.rds文件的结构和内容

# 加载必要的包
load_required_packages <- function() {
  required_packages <- c("Spectra", "MsExperiment", "jsonlite")
  
  for (pkg in required_packages) {
    if (!requireNamespace(pkg, quietly = TRUE)) {
      stop(paste("Required package", pkg, "is not installed. Please install it first."))
    }
  }
  
  library(Spectra)
  library(MsExperiment)
  library(jsonlite)
}

# 分析单个_spectra.rds文件
analyze_spectra_file <- function(rds_file_path) {
  tryCatch({
    cat("分析文件:", basename(rds_file_path), "\n")

    # 读取对象
    data_obj <- readRDS(rds_file_path)

    # 检查对象类型并提取Spectra对象
    if (inherits(data_obj, "MsExperiment")) {
      cat("  - 对象类型: MsExperiment\n")
      # 从MsExperiment中提取Spectra对象
      spectra_obj <- MsExperiment::spectra(data_obj)
    } else if (inherits(data_obj, "Spectra")) {
      cat("  - 对象类型: Spectra\n")
      spectra_obj <- data_obj
    } else {
      cat("  - 对象类型:", class(data_obj)[1], "\n")
      stop("不支持的对象类型")
    }

    # 基本信息
    basic_info <- list(
      file_name = basename(rds_file_path),
      total_spectra = length(spectra_obj),
      class_type = class(data_obj)[1]
    )

    cat("  - 总谱图数:", basic_info$total_spectra, "\n")

    # MS Level分析
    ms_levels <- Spectra::msLevel(spectra_obj)
    ms_level_summary <- table(ms_levels)

    cat("  - MS Level分布:\n")
    for (level in names(ms_level_summary)) {
      cat("    MS", level, ":", ms_level_summary[level], "个谱图\n")
    }

    # 获取spectraData (元数据)
    spectra_data <- Spectra::spectraData(spectra_obj)

    cat("  - spectraData列数:", ncol(spectra_data), "\n")
    cat("  - spectraData列名:\n")
    for (col_name in colnames(spectra_data)) {
      cat("    -", col_name, "\n")
    }

    # 分析MS1和MS2数据
    ms1_indices <- which(ms_levels == 1)
    ms2_indices <- which(ms_levels == 2)

    analysis_result <- list(
      basic_info = basic_info,
      ms_level_summary = as.list(ms_level_summary),
      spectra_data_columns = colnames(spectra_data),
      ms1_count = length(ms1_indices),
      ms2_count = length(ms2_indices)
    )

    # 如果有MS1数据，分析第一个MS1谱图的详细信息
    if (length(ms1_indices) > 0) {
      ms1_sample <- spectra_obj[ms1_indices[1]]
      ms1_peaks <- Spectra::peaksData(ms1_sample)[[1]]

      cat("  - MS1样本谱图峰数:", nrow(ms1_peaks), "\n")
      cat("  - MS1峰数据列名:", colnames(ms1_peaks), "\n")

      analysis_result$ms1_sample_info <- list(
        peak_count = nrow(ms1_peaks),
        peak_columns = colnames(ms1_peaks),
        mz_range = range(ms1_peaks[, "mz"], na.rm = TRUE),
        intensity_range = range(ms1_peaks[, "intensity"], na.rm = TRUE)
      )
    }

    # 如果有MS2数据，分析第一个MS2谱图的详细信息
    if (length(ms2_indices) > 0) {
      ms2_sample <- spectra_obj[ms2_indices[1]]
      ms2_peaks <- Spectra::peaksData(ms2_sample)[[1]]

      cat("  - MS2样本谱图峰数:", nrow(ms2_peaks), "\n")
      cat("  - MS2峰数据列名:", colnames(ms2_peaks), "\n")

      analysis_result$ms2_sample_info <- list(
        peak_count = nrow(ms2_peaks),
        peak_columns = colnames(ms2_peaks),
        mz_range = range(ms2_peaks[, "mz"], na.rm = TRUE),
        intensity_range = range(ms2_peaks[, "intensity"], na.rm = TRUE)
      )
    }

    return(analysis_result)

  }, error = function(e) {
    cat("分析文件失败:", e$message, "\n")
    return(NULL)
  })
}

# 批量分析目录中的所有_spectra.rds文件
analyze_spectra_directory <- function(cache_dir) {
  if (!dir.exists(cache_dir)) {
    stop("缓存目录不存在:", cache_dir)
  }
  
  # 查找所有_spectra.rds文件
  rds_files <- list.files(cache_dir, pattern = "_spectra\\.rds$", full.names = TRUE)
  
  if (length(rds_files) == 0) {
    cat("在目录中未找到_spectra.rds文件:", cache_dir, "\n")
    return(NULL)
  }
  
  cat("找到", length(rds_files), "个_spectra.rds文件\n")
  cat("开始分析...\n\n")
  
  all_results <- list()
  
  for (rds_file in rds_files) {
    result <- analyze_spectra_file(rds_file)
    if (!is.null(result)) {
      all_results[[basename(rds_file)]] <- result
    }
    cat("\n")
  }
  
  return(all_results)
}

# 生成分析报告
generate_analysis_report <- function(analysis_results, output_file = NULL) {
  if (is.null(analysis_results) || length(analysis_results) == 0) {
    cat("没有分析结果可生成报告\n")
    return(NULL)
  }
  
  # 汇总统计
  total_files <- length(analysis_results)
  total_spectra <- sum(sapply(analysis_results, function(x) x$basic_info$total_spectra))
  total_ms1 <- sum(sapply(analysis_results, function(x) x$ms1_count))
  total_ms2 <- sum(sapply(analysis_results, function(x) x$ms2_count))
  
  # 获取所有spectraData列名
  all_columns <- unique(unlist(lapply(analysis_results, function(x) x$spectra_data_columns)))
  
  report <- list(
    summary = list(
      total_files = total_files,
      total_spectra = total_spectra,
      total_ms1_spectra = total_ms1,
      total_ms2_spectra = total_ms2,
      common_spectra_data_columns = all_columns
    ),
    file_details = analysis_results
  )
  
  # 打印报告摘要
  cat("=== Spectra数据分析报告 ===\n")
  cat("分析文件数:", total_files, "\n")
  cat("总谱图数:", total_spectra, "\n")
  cat("MS1谱图数:", total_ms1, "\n")
  cat("MS2谱图数:", total_ms2, "\n")
  cat("spectraData常见列:\n")
  for (col in all_columns) {
    cat("  -", col, "\n")
  }
  
  # 保存报告到文件
  if (!is.null(output_file)) {
    jsonlite::write_json(report, output_file, pretty = TRUE, auto_unbox = TRUE)
    cat("\n报告已保存到:", output_file, "\n")
  }
  
  return(report)
}

# 主分析函数
main_analysis <- function(project_root = NULL) {
  if (is.null(project_root)) {
    project_root <- "test/test4"  # 默认使用test4项目
  }
  
  cache_dir <- file.path(project_root, "data", "cache", "spectra_v2")
  output_file <- file.path(project_root, "data", "spectra_analysis_report.json")
  
  cat("开始分析项目:", project_root, "\n")
  cat("缓存目录:", cache_dir, "\n\n")
  
  # 加载必要的包
  load_required_packages()
  
  # 分析所有文件
  results <- analyze_spectra_directory(cache_dir)
  
  # 生成报告
  if (!is.null(results)) {
    report <- generate_analysis_report(results, output_file)
    return(report)
  } else {
    cat("没有找到可分析的数据\n")
    return(NULL)
  }
}
