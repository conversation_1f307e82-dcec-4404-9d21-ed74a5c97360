# 全局配置和初始化
# 在应用启动时执行

# 设置工作目录
if (!exists("PROJECT_ROOT")) {
  PROJECT_ROOT <- getwd()
}

# 创建软件安装路径的必要目录结构
create_app_dirs <- function() {
  dirs <- c(
    "data/config",  # 只保留全局配置目录
    "logs"          # 日志目录
  )

  for (dir in dirs) {
    if (!dir.exists(dir)) {
      dir.create(dir, recursive = TRUE)
    }
  }
}

# 初始化软件安装路径目录
create_app_dirs()

# 全局变量（仅用于软件级别的配置）
GLOBAL_CONFIG <- list(
  app_name = "实验室实时质控系统",
  version = "1.0.0",
  app_root = PROJECT_ROOT,
  global_config_path = file.path(PROJECT_ROOT, "data/config")  # 全局配置路径
)

# 日志系统将在logger.R加载后初始化

# 加载工具模块（按依赖顺序加载）
source(file.path("utils", "path_manager.R"), encoding = "UTF-8")      # 路径管理（核心）
source(file.path("utils", "project_manager.R"), encoding = "UTF-8")   # 项目管理
source(file.path("utils", "data_index_manager.R"), encoding = "UTF-8") # 数据索引管理
source(file.path("utils", "monitor_ions_manager.R"), encoding = "UTF-8")  # 监控离子管理
source(file.path("utils", "monitoring_controller.R"), encoding = "UTF-8") # 监控控制器
source(file.path("utils", "data_processor.R"), encoding = "UTF-8")    # 数据处理器