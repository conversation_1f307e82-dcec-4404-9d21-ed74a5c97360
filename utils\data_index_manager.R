# 数据索引管理器
# 基于路径引用的数据管理系统，不复制数据文件

# 辅助函数：格式化文件大小
format_file_size <- function(bytes) {
  if (is.na(bytes) || bytes == 0) return("0 B")

  units <- c("B", "KB", "MB", "GB", "TB")
  unit_index <- 1
  size <- bytes

  while (size >= 1024 && unit_index < length(units)) {
    size <- size / 1024
    unit_index <- unit_index + 1
  }

  if (unit_index == 1) {
    return(paste(size, units[unit_index]))
  } else {
    return(paste(round(size, 2), units[unit_index]))
  }
}

# 辅助函数：安全提取字段值
safe_extract_field <- function(value, default = "") {
  # 处理NULL值
  if (is.null(value)) {
    return(default)
  }

  # 处理空列表或空对象
  if (is.list(value) && length(value) == 0) {
    return(default)
  }

  # 处理字符向量
  if (is.character(value) && length(value) > 0) {
    return(value[1])  # 取第一个元素
  }

  # 处理其他类型
  if (length(value) == 1) {
    return(as.character(value))
  }

  # 默认返回
  return(default)
}

# 数据索引结构定义
create_data_index_structure <- function() {
  list(
    version = "1.0",
    created_time = format(Sys.time(), "%Y-%m-%d %H:%M:%S"),
    last_updated = format(Sys.time(), "%Y-%m-%d %H:%M:%S"),
    data_files = list(),
    statistics = list(
      total_files = 0,
      by_status = list(
        "已处理" = 0,
        "处理中" = 0,
        "待处理" = 0,
        "失败" = 0
      ),
      by_type = list(
        "QC" = 0,
        "STD" = 0,
        "BLANK" = 0,
        "SAMPLE" = 0
      )
    )
  )
}

# 辅助函数：根据文件名自动识别样本类型
detect_sample_type <- function(file_name) {
  # 将文件名转为小写以便于匹配
  lower_name <- tolower(file_name)
  
  # 默认样本类型为实验样本
  sample_type <- "SAMPLE"
  
  # 检查QC样本 - 更明确的匹配
  if (grepl("\\bqc\\b|_qc_|^qc_|_qc$", lower_name)) {
    sample_type <- "QC"
    return(sample_type)
  }
  
  # 检查空白样本 - 更明确的匹配
  if (grepl("\\bblank\\b|_blank_|^blank_|_blank$", lower_name)) {
    sample_type <- "BLANK"
    return(sample_type)
  }
  
  # 检查标准品样本 - 更明确的匹配
  if (grepl("\\bstand\\b|_stand_|^stand_|_stand$|\\bstd\\b|_std_|^std_|_std$|\\bcalib\\b|_calib_|^calib_|_calib$", lower_name)) {
    sample_type <- "STD"
    return(sample_type)
  }
  
  # 检查文件名中是否包含QC、BLANK、STD、STAND的完整单词
  if (grepl("qc|blank|std|stand|calib", lower_name, ignore.case = TRUE)) {
    # 如果包含这些关键词，根据最匹配的确定类型
    if (grepl("qc", lower_name, ignore.case = TRUE)) {
      sample_type <- "QC"
    } else if (grepl("blank", lower_name, ignore.case = TRUE)) {
      sample_type <- "BLANK"
    } else if (grepl("stand|std|calib", lower_name, ignore.case = TRUE)) {
      sample_type <- "STD"
    }
  }
  
  return(sample_type)
}

# 辅助函数：从文件路径识别离子扫描模式
detect_scan_mode <- function(file_path) {
  # 将路径转为小写以便于匹配
  lower_path <- tolower(file_path)
  
  # 默认扫描模式
  scan_mode <- "Unknown"
  
  # 检查负离子模式
  if (grepl("negative|neg", lower_path, ignore.case = TRUE)) {
    scan_mode <- "negative"
  }
  
  # 检查正离子模式
  if (grepl("positive|pos", lower_path, ignore.case = TRUE)) {
    scan_mode <- "positive"
  }
  
  # 检查简写形式
  if (grepl("neg", lower_path, ignore.case = TRUE)) {
    scan_mode <- "negative"
  }
  
  if (grepl("pos", lower_path, ignore.case = TRUE)) {
    scan_mode <- "positive"
  }
  
  return(scan_mode)
}

# 创建数据文件记录
create_data_file_record <- function(file_path, file_name = NULL, sample_type = NULL,
                                   status = "待处理", notes = "", auto_detect = TRUE) {
  
  # 标准化路径
  file_path <- normalize_absolute_path(file_path)
  
  # 自动检测文件信息
  if (auto_detect && file.exists(file_path)) {
    tryCatch({
      file_info <- file.info(file_path)
      file_size <- format_file_size(file_info$size)
      file_ext <- tools::file_ext(file_path)

      # 如果没有提供文件名，使用basename
      if (is.null(file_name) || file_name == "") {
        file_name <- basename(file_path)
      }

      # 根据扩展名确定文件类型
      file_type <- switch(toupper(file_ext),
                         "RAW" = "Thermo RAW",
                         "MZML" = "mzML",
                         "MZXML" = "mzXML",
                         "MGF" = "MGF",
                         "D" = "Agilent .d",
                         toupper(file_ext))
      
      # 自动识别样本类型
      sample_type <- detect_sample_type(file_name)
      
      # 自动识别扫描模式
      scan_mode <- detect_scan_mode(file_path)
      
    }, error = function(e) {
      # 如果获取文件信息失败，使用默认值
      file_size <<- "未知"
      file_type <<- tools::file_ext(file_path)
      if (is.null(file_name) || file_name == "") {
        file_name <<- basename(file_path)
      }
      if (is.null(sample_type)) {
        sample_type <<- "SAMPLE"  # 默认样本类型
      }
      scan_mode <<- "Unknown"
    })
  } else {
    # 文件不存在或不自动检测
    file_size <- if (auto_detect) "文件不存在" else "待生成"
    file_ext <- tools::file_ext(file_path)
    file_type <- switch(toupper(file_ext),
                       "RAW" = "Thermo RAW",
                       "MZML" = "mzML",
                       "MZXML" = "mzXML",
                       "MGF" = "MGF",
                       "D" = "Agilent .d",
                       if (file_ext != "") toupper(file_ext) else "未知")
    if (is.null(file_name) || file_name == "") {
      file_name <- basename(file_path)
    }
    if (is.null(sample_type)) {
      sample_type <- detect_sample_type(file_name)  # 即使文件不存在也尝试识别
    }
    scan_mode <- detect_scan_mode(file_path)  # 即使文件不存在也尝试识别
  }
  
  # 生成唯一ID
  file_id <- paste0("file_", format(Sys.time(), "%Y%m%d_%H%M%S_"),
                   sample(1000:9999, 1))
  
  list(
    id = file_id,
    file_name = file_name %||% "",
    file_path = file_path %||% "",
    file_size = file_size %||% "未知",
    file_type = file_type %||% "未知",
    sample_type = sample_type %||% "SAMPLE",
    scan_mode = scan_mode %||% "Unknown",
    status = status %||% "待处理",
    notes = notes %||% "",
    created_time = format(Sys.time(), "%Y-%m-%d %H:%M:%S"),
    last_modified = format(Sys.time(), "%Y-%m-%d %H:%M:%S"),
    last_accessed = "",
    processing_history = list()
  )
}

# 获取项目数据索引文件路径
get_data_index_path <- function(project_name = NULL) {
  # 获取当前项目根路径
  project_root <- get_project_root_path()
  if (is.null(project_root)) {
    stop("无法获取项目根路径，请确保已创建或导入项目")
  }

  # 数据索引文件存储在项目的data目录下
  index_path <- file.path(project_root, "data", "data_index.json")

  # 确保data目录存在
  data_dir <- dirname(index_path)
  if (!dir.exists(data_dir)) {
    dir.create(data_dir, recursive = TRUE, showWarnings = FALSE)
    log_info(paste("创建项目数据目录:", data_dir))
  }

  return(index_path)
}

# 加载数据索引
load_data_index <- function(project_name = NULL) {
  index_path <- get_data_index_path(project_name)
  
  if (!file.exists(index_path)) {
    # 创建新的索引文件
    index <- create_data_index_structure()
    save_data_index(index, project_name)
    return(index)
  }
  
  tryCatch({
    jsonlite::fromJSON(index_path, simplifyVector = FALSE)
  }, error = function(e) {
    log_warning(paste("加载数据索引失败，创建新索引:", e$message))
    create_data_index_structure()
  })
}

# 保存数据索引
save_data_index <- function(index, project_name = NULL) {
  index_path <- get_data_index_path(project_name)
  
  # 确保目录存在
  dir.create(dirname(index_path), recursive = TRUE, showWarnings = FALSE)
  
  # 更新时间戳
  index$last_updated <- format(Sys.time(), "%Y-%m-%d %H:%M:%S")
  
  # 更新统计信息
  index <- update_index_statistics(index)
  
  tryCatch({
    jsonlite::write_json(index, index_path, pretty = TRUE, auto_unbox = TRUE)
    log_info(paste("数据索引已保存:", index_path))
  }, error = function(e) {
    log_error(paste("保存数据索引失败:", e$message))
    stop(paste("保存数据索引失败:", e$message))
  })
}

# 更新索引统计信息
update_index_statistics <- function(index) {
  files <- index$data_files

  # 总文件数
  index$statistics$total_files <- length(files)

  # 按状态统计
  if (length(files) > 0) {
    status_values <- sapply(files, function(f) f$status %||% "未知")
    status_counts <- table(status_values)
    for (status in names(index$statistics$by_status)) {
      index$statistics$by_status[[status]] <- as.numeric(status_counts[status] %||% 0)
    }
  } else {
    # 如果没有文件，重置所有计数为0
    for (status in names(index$statistics$by_status)) {
      index$statistics$by_status[[status]] <- 0
    }
  }

  # 按类型统计
  if (length(files) > 0) {
    type_values <- sapply(files, function(f) f$sample_type %||% "未知")
    type_counts <- table(type_values)
    for (type in names(index$statistics$by_type)) {
      index$statistics$by_type[[type]] <- as.numeric(type_counts[type] %||% 0)
    }
  } else {
    # 如果没有文件，重置所有计数为0
    for (type in names(index$statistics$by_type)) {
      index$statistics$by_type[[type]] <- 0
    }
  }

  index
}

# 添加数据文件到索引
add_file_to_index <- function(file_path, file_name = NULL, sample_type = "QC",
                             status = "待处理", notes = "", project_name = NULL,
                             allow_missing = FALSE) {

  # 检查文件是否存在（除非允许添加不存在的文件）
  if (!file.exists(file_path) && !allow_missing) {
    stop(paste("文件不存在:", file_path))
  }

  # 加载索引
  index <- load_data_index(project_name)

  # 检查文件是否已存在
  existing_file <- find_file_in_index(file_path, index)
  if (!is.null(existing_file)) {
    log_warning(paste("文件已存在于索引中:", file_path))
    return(existing_file$id)
  }

  # 创建文件记录
  file_record <- create_data_file_record(file_path, file_name, sample_type, status, notes,
                                        auto_detect = file.exists(file_path))

  # 添加到索引
  index$data_files[[file_record$id]] <- file_record

  # 保存索引
  save_data_index(index, project_name)

  log_info(paste("文件已添加到索引:", file_record$file_name))
  return(file_record$id)
}

# 在索引中查找文件
find_file_in_index <- function(file_path, index = NULL) {
  if (is.null(index)) {
    index <- load_data_index()
  }
  
  file_path <- normalize_absolute_path(file_path)
  
  for (file_record in index$data_files) {
    if (file_record$file_path == file_path) {
      return(file_record)
    }
  }
  
  return(NULL)
}

# 批量添加文件夹中的文件
add_folder_to_index <- function(folder_path, file_pattern = NULL, include_subfolders = TRUE,
                               default_sample_type = "QC", project_name = NULL) {
  
  if (!dir.exists(folder_path)) {
    stop(paste("文件夹不存在:", folder_path))
  }
  
  # 获取文件列表
  files <- character(0)

  tryCatch({
    # 检查路径是否存在
    if (!dir.exists(folder_path)) {
      log_warning(paste("文件夹不存在:", folder_path))
      return(character(0))
    }

    if (is.null(file_pattern) || file_pattern == "all") {
      # 支持的文件扩展名（使用正则表达式）
      pattern <- "\\.(raw|mzML|mzXML|mgf|d)$"
    } else {
      # 特定文件类型
      pattern <- switch(file_pattern,
                       "raw" = "\\.raw$",
                       "mzML" = "\\.mzML$",
                       "mzXML" = "\\.mzXML$",
                       "mgf" = "\\.mgf$",
                       paste0("\\.", file_pattern, "$"))
    }

    # 使用正则表达式搜索文件
    files <- list.files(folder_path, pattern = pattern,
                       recursive = include_subfolders,
                       full.names = TRUE, ignore.case = TRUE)

    if (length(files) == 0) {
      log_warning(paste("在文件夹中未找到匹配的文件:", folder_path))
    }

  }, error = function(e) {
    log_error(paste("扫描文件夹失败:", e$message))
  })
  
  if (length(files) == 0) {
    log_warning(paste("在文件夹中未找到支持的数据文件:", folder_path))
    return(character(0))
  }
  
  # 批量添加文件
  added_files <- character(0)
  for (file_path in files) {
    tryCatch({
      file_id <- add_file_to_index(file_path, file_name = basename(file_path),
                                  sample_type = default_sample_type,
                                  status = "待处理", notes = "",
                                  project_name = project_name)
      added_files <- c(added_files, file_id)
    }, error = function(e) {
      log_warning(paste("添加文件失败:", file_path, "-", e$message))
    })
  }
  
  log_info(paste("批量添加完成，成功添加", length(added_files), "个文件"))
  return(added_files)
}

# 使用列映射从CSV导入文件索引
import_from_csv_with_mapping <- function(csv_path, column_mapping, skip_rows = 0, project_name = NULL) {
  if (!file.exists(csv_path)) {
    stop(paste("CSV文件不存在:", csv_path))
  }

  tryCatch({
    # 读取CSV数据
    csv_data <- read.csv(csv_path, stringsAsFactors = FALSE, fileEncoding = "UTF-8",
                        skip = skip_rows, header = TRUE)

    # 标准化列名
    names(csv_data) <- trimws(names(csv_data))

    # 验证映射的列是否存在
    required_columns <- c(column_mapping$filename, column_mapping$filepath)
    missing_columns <- setdiff(required_columns, names(csv_data))

    if (length(missing_columns) > 0) {
      stop(paste("CSV文件中缺少映射的列:", paste(missing_columns, collapse = ", ")))
    }

    # 批量添加文件
    added_files <- character(0)
    for (i in 1:nrow(csv_data)) {
      row <- csv_data[i, ]

      # 根据列映射提取数据
      file_name <- row[[column_mapping$filename]]
      file_path <- row[[column_mapping$filepath]]

      # 可选列
      sample_type <- if (!is.null(column_mapping$sampletype)) {
        row[[column_mapping$sampletype]] %||% "Unknown"
      } else {
        "Unknown"
      }

      notes <- if (!is.null(column_mapping$notes)) {
        row[[column_mapping$notes]] %||% ""
      } else {
        ""
      }

      # 跳过空行
      if (is.na(file_name) || is.na(file_path) || file_name == "" || file_path == "") {
        next
      }

      # 构建完整文件路径（如果路径不包含文件名）
      if (!grepl("\\.(raw|mzML|mzXML|mgf|d)$", file_path, ignore.case = TRUE)) {
        # 路径是文件夹，需要添加文件名和扩展名
        full_path <- file.path(file_path, paste0(file_name, ".raw"))
      } else {
        # 路径已经是完整文件路径
        full_path <- file_path
      }

      tryCatch({
        file_id <- add_file_to_index(full_path, file_name = file_name,
                                    sample_type = sample_type, status = "待处理",
                                    notes = notes, project_name = project_name,
                                    allow_missing = TRUE)
        added_files <- c(added_files, file_id)
      }, error = function(e) {
        log_warning(paste("添加文件失败 (行", i, "):", file_name, "-", e$message))
      })
    }

    log_info(paste("CSV导入完成，成功添加", length(added_files), "个文件"))
    return(added_files)

  }, error = function(e) {
    stop(paste("CSV导入失败:", e$message))
  })
}

# 从CSV导入文件索引
import_from_csv <- function(csv_path, project_name = NULL) {
  if (!file.exists(csv_path)) {
    stop(paste("CSV文件不存在:", csv_path))
  }

  tryCatch({
    # 先读取前几行检查格式
    first_lines <- readLines(csv_path, n = 3, encoding = "UTF-8")

    # 检查是否有元数据行（如 Bracket Type=4）
    skip_rows <- 0
    if (length(first_lines) > 0) {
      # 检查第一行是否是元数据行
      first_line <- first_lines[1]
      # 如果第一行包含"="且以"Bracket Type"开头，则跳过
      if (grepl("^Bracket Type=", first_line, ignore.case = TRUE)) {
        skip_rows <- 1
      }
    }

    # 读取CSV数据
    csv_data <- read.csv(csv_path, stringsAsFactors = FALSE, fileEncoding = "UTF-8",
                        skip = skip_rows, header = TRUE)

    # 标准化列名（处理可能的空格和大小写问题）
    names(csv_data) <- trimws(names(csv_data))

    # 检查必需的列（支持英文列名，考虑空格转换为点号）
    required_cols_cn <- c("文件名", "路径")
    required_cols_en <- c("File Name", "Path")
    required_cols_en_dot <- c("File.Name", "Path")  # R会将空格转换为点号

    # 检查中文列名
    missing_cols_cn <- setdiff(required_cols_cn, names(csv_data))
    # 检查英文列名（原始格式）
    missing_cols_en <- setdiff(required_cols_en, names(csv_data))
    # 检查英文列名（点号格式）
    missing_cols_en_dot <- setdiff(required_cols_en_dot, names(csv_data))

    # 如果所有格式都不完整，报错
    if (length(missing_cols_cn) > 0 && length(missing_cols_en) > 0 && length(missing_cols_en_dot) > 0) {
      stop(paste("CSV文件缺少必需的列:", paste(required_cols_cn, collapse = ", "),
                "或", paste(required_cols_en, collapse = ", ")))
    }

    # 确定使用哪种列名格式
    if (length(missing_cols_cn) == 0) {
      use_format <- "chinese"
    } else if (length(missing_cols_en) == 0) {
      use_format <- "english"
    } else {
      use_format <- "english_dot"
    }

    # 批量添加文件
    added_files <- character(0)
    for (i in 1:nrow(csv_data)) {
      row <- csv_data[i, ]

      # 根据列名格式提取数据
      if (use_format == "chinese") {
        file_name <- row$文件名
        file_path <- row$路径
        sample_type <- row$样本类型 %||% "QC"
        notes <- row$备注 %||% ""
      } else if (use_format == "english") {
        file_name <- row$`File Name`
        file_path <- row$Path
        sample_type <- row$`Sample Type` %||% "Unknown"
        notes <- row$Comment %||% ""
      } else {  # english_dot
        file_name <- row$File.Name
        file_path <- row$Path
        sample_type <- row$Sample.Type %||% "Unknown"
        notes <- row$Comment %||% ""
      }

      # 跳过空行
      if (is.na(file_name) || is.na(file_path) || file_name == "" || file_path == "") {
        next
      }

      # 构建完整文件路径（如果路径不包含文件名）
      if (!grepl("\\.(raw|mzML|mzXML|mgf|d)$", file_path, ignore.case = TRUE)) {
        # 路径是文件夹，需要添加文件名和扩展名
        full_path <- file.path(file_path, paste0(file_name, ".raw"))
      } else {
        # 路径已经是完整文件路径
        full_path <- file_path
      }

      tryCatch({
        file_id <- add_file_to_index(full_path, file_name = file_name,
                                    sample_type = sample_type, status = "待处理",
                                    notes = notes, project_name = project_name,
                                    allow_missing = TRUE)
        added_files <- c(added_files, file_id)
      }, error = function(e) {
        log_warning(paste("添加文件失败 (行", i, "):", file_name, "-", e$message))
      })
    }

    log_info(paste("CSV导入完成，成功添加", length(added_files), "个文件"))
    return(added_files)
    
  }, error = function(e) {
    log_error(paste("CSV导入失败:", e$message))
    stop(paste("CSV导入失败:", e$message))
  })
}

# 获取数据索引的数据框表示（用于UI显示）
get_data_index_dataframe <- function(project_name = NULL) {
  index <- load_data_index(project_name)

  if (length(index$data_files) == 0) {
    # 返回空的数据框
    return(data.frame(
      文件名 = character(0),
      路径 = character(0),
      大小 = character(0),
      状态 = character(0),
      类型 = character(0),
      样本类型 = character(0),
      修改时间 = character(0),
      备注 = character(0),
      stringsAsFactors = FALSE
    ))
  }

  # 直接构建数据框，避免使用rbind
  file_ids <- names(index$data_files)
  n <- length(file_ids)

  # 创建向量
  file_names <- character(n)
  file_paths <- character(n)
  file_sizes <- character(n)
  file_statuses <- character(n)
  file_types <- character(n)
  sample_types <- character(n)
  modified_times <- character(n)
  notes <- character(n)

  # 填充向量
  for (i in 1:n) {
    file_record <- index$data_files[[file_ids[i]]]

    # 安全地提取字段，处理可能的异常值
    file_names[i] <- safe_extract_field(file_record$file_name, "")
    file_paths[i] <- safe_extract_field(file_record$file_path, "")
    file_sizes[i] <- safe_extract_field(file_record$file_size, "未知")
    file_statuses[i] <- safe_extract_field(file_record$status, "未知")
    file_types[i] <- safe_extract_field(file_record$file_type, "未知")
    sample_types[i] <- safe_extract_field(file_record$sample_type, "未知")
    modified_times[i] <- safe_extract_field(file_record$last_modified, "")
    notes[i] <- safe_extract_field(file_record$notes, "")
  }

  # 创建数据框
  result <- data.frame(
    文件名 = file_names,
    路径 = file_paths,
    大小 = file_sizes,
    状态 = file_statuses,
    类型 = file_types,
    样本类型 = sample_types,
    扫描模式 = character(n),  # 添加扫描模式列
    修改时间 = modified_times,
    备注 = notes,
    stringsAsFactors = FALSE
  )
  
  # 填充扫描模式
  for (i in 1:n) {
    file_record <- index$data_files[[file_ids[i]]]
    result$扫描模式[i] <- safe_extract_field(file_record$scan_mode, "未知")
  }

  return(result)
}

# 更新数据文件记录
update_file_in_index <- function(file_id, updates, project_name = NULL) {
  index <- load_data_index(project_name)

  if (!file_id %in% names(index$data_files)) {
    stop(paste("文件ID不存在:", file_id))
  }

  # 更新文件记录
  file_record <- index$data_files[[file_id]]

  # 应用更新
  for (field in names(updates)) {
    file_record[[field]] <- updates[[field]]
  }

  # 更新修改时间
  file_record$last_modified <- format(Sys.time(), "%Y-%m-%d %H:%M:%S")

  # 保存回索引
  index$data_files[[file_id]] <- file_record

  # 保存索引
  save_data_index(index, project_name)

  log_info(paste("文件记录已更新:", file_record$file_name))
  return(file_id)
}

# 从索引中删除文件
remove_file_from_index <- function(file_id, project_name = NULL) {
  index <- load_data_index(project_name)

  if (!file_id %in% names(index$data_files)) {
    stop(paste("文件ID不存在:", file_id))
  }

  file_name <- index$data_files[[file_id]]$file_name

  # 从索引中删除
  index$data_files[[file_id]] <- NULL

  # 保存索引
  save_data_index(index, project_name)

  log_info(paste("文件已从索引中删除:", file_name))
  return(TRUE)
}

# 批量删除文件
remove_files_from_index <- function(file_ids, project_name = NULL) {
  index <- load_data_index(project_name)

  removed_count <- 0
  for (file_id in file_ids) {
    if (file_id %in% names(index$data_files)) {
      index$data_files[[file_id]] <- NULL
      removed_count <- removed_count + 1
    }
  }

  if (removed_count > 0) {
    # 保存索引
    save_data_index(index, project_name)
    log_info(paste("批量删除完成，删除", removed_count, "个文件"))
  }

  return(removed_count)
}

# 根据行索引获取文件ID
get_file_id_by_row_index <- function(row_index, project_name = NULL) {
  index <- load_data_index(project_name)

  if (length(index$data_files) == 0) {
    return(NULL)
  }

  file_ids <- names(index$data_files)

  if (row_index < 1 || row_index > length(file_ids)) {
    return(NULL)
  }

  return(file_ids[row_index])
}

# 根据行索引批量获取文件ID
get_file_ids_by_row_indices <- function(row_indices, project_name = NULL) {
  index <- load_data_index(project_name)

  if (length(index$data_files) == 0) {
    return(character(0))
  }

  file_ids <- names(index$data_files)
  valid_indices <- row_indices[row_indices >= 1 & row_indices <= length(file_ids)]

  return(file_ids[valid_indices])
}
