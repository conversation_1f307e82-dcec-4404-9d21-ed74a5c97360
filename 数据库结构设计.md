# 数据库结构设计
## 原始数据
每条原始数据都包含多次质谱扫描的元数据(spectraData)
重要字段如下：
"msLevel" 质谱扫描的层数，1为MS1，2为MS2
"rtime" 扫描时间
"scanIndex" 扫描编号
"precScanNum" msLevel为2时，该次扫描对应的前体离子来源msLevel 1的扫描编号
"precursorMz" msLevel为2时，该次扫描对应的前体离子的mz
"precursorIntensity" msLevel为2时，该次扫描对应的前体离子的强度
"precursorCharge" msLevel为2时，该次扫描对应前体离子的电荷
"collisionEnergy" msLevel为2时，该次扫描对应的离子碎裂时使用的碰撞能
"isolationWindowLowerMz" msLevel为2时，该次扫描对应的前体离子的隔离窗口下限
"isolationWindowTargetMz" msLevel为2时，该次扫描对应的前体离子的隔离窗口中心
"isolationWindowUpperMz" msLevel为2时，该次扫描对应前体离子的隔离窗口上限
"peaksCount" 谱图数据点的数目
"totIonCurrent" 谱图数据的总强度
"basePeakMZ" 谱图数据的基峰mz
"basePeakIntensity" 谱图数据的基峰强度
"lowMZ" 谱图数据的最小mz
"highMZ" 谱图数据的最大mz
"injectionTime" 谱图数据的注入时间
"scanWindowLowerLimit" 扫描窗口下限
"scanWindowUpperLimit" 扫描窗口上限

谱图数据(peaksData)
重要字段如下：
"mz" 谱图数据的mz
"intensity" 谱图数据的强度
