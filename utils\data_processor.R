# Utils层 - 数据处理器
# 负责处理原始数据文件并生成结果文件

# 处理上传的文件
process_uploaded_files <- function(file_paths, project_name = NULL) {
  tryCatch({
    log_info(paste("开始处理", length(file_paths), "个上传文件"))
    
    # 如果没有提供项目名称，尝试获取当前项目
    if (is.null(project_name)) {
      project_name <- get_current_project()
    }
    
    if (is.null(project_name)) {
      stop("无法确定项目名称，无法处理文件")
    }
    
    # 这里应该实现实际的数据处理逻辑
    # 根据文件类型进行不同的处理
    
    for (file_path in file_paths) {
      file_name <- basename(file_path)
      file_ext <- tools::file_ext(file_name)
      
      log_info(paste("处理文件:", file_name, "类型:", file_ext))
      
      # 根据文件类型进行处理
      if (file_ext %in% c("raw", "mzML", "mzXML", "mgf", "csv", "txt")) {
        process_file_by_type(file_path, file_ext, project_name)
      } else {
        log_warning(paste("不支持的文件类型:", file_ext, "文件:", file_name))
      }
    }
    
    log_info("文件处理完成")
    return(list(success = TRUE, message = "文件处理成功"))
    
  }, error = function(e) {
    log_error(paste("文件处理失败:", e$message))
    return(list(success = FALSE, message = paste("文件处理失败:", e$message)))
  })
}

# 通用文件处理函数
process_file_by_type <- function(file_path, file_type, project_name) {
  log_info(paste("处理", toupper(file_type), "文件:", basename(file_path)))

  # 实际的文件处理逻辑应该在这里实现
  # 目前使用数据索引管理器来处理文件

  # 生成处理结果
  generate_processing_results(file_path, file_type, project_name)
}

# 生成处理结果
generate_processing_results <- function(file_path, file_type, project_name) {
  file_name <- basename(file_path)
  file_name_no_ext <- tools::file_path_sans_ext(file_name)
  
  # 使用提供的项目名称
  if (is.null(project_name)) {
    log_error("项目名称不能为空")
    return()
  }
  
  project_path <- get_project_path(project_name)
  results_dir <- file.path(project_path, "results")
  
  if (!dir.exists(results_dir)) {
    dir.create(results_dir, recursive = TRUE)
  }
  
  # 生成时间序列数据
  current_time <- Sys.time()
  time_points <- seq(current_time - 3600, current_time, by = "1 min")
  
  # 生成TIC数据
  tic_data <- data.frame(
    time = format(time_points, "%H:%M:%S"),
    value = round(runif(length(time_points), 1000, 10000), 2),
    stringsAsFactors = FALSE
  )
  
  # 生成BPC数据
  bpc_data <- data.frame(
    time = format(time_points, "%H:%M:%S"),
    value = round(runif(length(time_points), 500, 8000), 2),
    stringsAsFactors = FALSE
  )
  
  # 生成EIC数据
  eic_data <- data.frame(
    time = format(time_points, "%H:%M:%S"),
    value = round(runif(length(time_points), 200, 5000), 2),
    stringsAsFactors = FALSE
  )
  
  # 生成监控结果数据
  time_points_5min <- seq(current_time - 3600, current_time, by = "5 min")
  monitoring_results <- data.frame(
    时间 = format(time_points_5min, "%H:%M:%S"),
    类型 = rep(c("TIC", "BPC", "EIC"), each = length(time_points_5min)),
    值 = round(runif(length(time_points_5min) * 3, 1000, 10000), 2),
    状态 = rep("正常", length(time_points_5min) * 3),
    stringsAsFactors = FALSE
  )
  
  # 保存结果文件
  write.csv(tic_data, file.path(results_dir, "tic_data.csv"), row.names = FALSE, fileEncoding = "UTF-8")
  write.csv(bpc_data, file.path(results_dir, "bpc_data.csv"), row.names = FALSE, fileEncoding = "UTF-8")
  write.csv(eic_data, file.path(results_dir, "eic_data.csv"), row.names = FALSE, fileEncoding = "UTF-8")
  write.csv(monitoring_results, file.path(results_dir, "monitoring_results.csv"), row.names = FALSE, fileEncoding = "UTF-8")
  
  log_info(paste("已生成处理结果文件:", file_name))
}

# 获取当前项目（从全局变量或环境变量中获取）
get_current_project <- function() {
  # 这里应该从全局变量或环境变量中获取当前项目名称
  # 暂时返回NULL，实际使用时需要根据具体情况实现
  # 在实际应用中，这个函数应该从Shiny的reactive变量中获取当前项目
  return(NULL)
}

# 启动文件夹监控
start_folder_monitoring <- function(monitor_path, include_subfolders, file_pattern) {
  tryCatch({
    log_info(paste("启动文件夹监控:", monitor_path))
    log_info(paste("包含子文件夹:", include_subfolders))
    log_info(paste("文件模式:", file_pattern))
    
    # 这里应该实现实际的文件夹监控逻辑
    # 例如：设置文件系统监控、定时检查等
    
    # 模拟监控启动
    Sys.sleep(1)
    
    log_info("文件夹监控已启动")
    return(list(success = TRUE, message = "文件夹监控启动成功"))
    
  }, error = function(e) {
    log_error(paste("启动文件夹监控失败:", e$message))
    return(list(success = FALSE, message = paste("启动文件夹监控失败:", e$message)))
  })
}

# 处理文件夹中的文件
process_folder_files <- function(file_paths, project_name = NULL) {
  tryCatch({
    log_info(paste("开始处理文件夹中的", length(file_paths), "个文件"))

    if (is.null(project_name)) {
      project_name <- get_current_project()
    }

    if (is.null(project_name)) {
      stop("无法确定项目名称，无法处理文件")
    }

    # 创建数据列表
    data_list <- data.frame(
      文件名 = character(),
      路径 = character(),
      大小 = character(),
      状态 = character(),
      类型 = character(),
      修改时间 = character(),
      stringsAsFactors = FALSE
    )

    successful_count <- 0
    failed_count <- 0

    for (file_path in file_paths) {
      tryCatch({
        file_name <- basename(file_path)
        file_info <- file.info(file_path)
        file_size <- round(file_info$size / 1024 / 1024, 2)  # MB
        file_ext <- tools::file_ext(file_name)

        # 添加到数据列表
        new_row <- data.frame(
          文件名 = file_name,
          路径 = file_path,
          大小 = paste(file_size, "MB"),
          状态 = "已识别",
          类型 = toupper(file_ext),
          修改时间 = format(file_info$mtime, "%Y-%m-%d %H:%M:%S"),
          stringsAsFactors = FALSE
        )

        data_list <- rbind(data_list, new_row)
        successful_count <- successful_count + 1

        log_info(paste("已识别文件:", file_name))

      }, error = function(e) {
        failed_count <- failed_count + 1
        log_error(paste("处理文件失败:", basename(file_path), "错误:", e$message))
      })
    }

    # 保存数据列表到项目目录
    save_data_list(data_list, project_name)

    log_info(paste("文件夹处理完成，成功:", successful_count, "失败:", failed_count))
    return(list(
      success = TRUE,
      message = paste("文件夹处理完成，成功识别", successful_count, "个文件"),
      data = data_list
    ))

  }, error = function(e) {
    log_error(paste("文件夹处理失败:", e$message))
    return(list(success = FALSE, message = paste("文件夹处理失败:", e$message)))
  })
}

# 处理单个文件
process_single_file <- function(file_info, project_name = NULL) {
  tryCatch({
    log_info(paste("开始处理单个文件:", file_info$path))

    if (is.null(project_name)) {
      project_name <- get_current_project()
    }

    if (is.null(project_name)) {
      stop("无法确定项目名称，无法处理文件")
    }

    file_path <- file_info$path
    file_name <- basename(file_path)
    file_stat <- file.info(file_path)
    file_size <- round(file_stat$size / 1024 / 1024, 2)  # MB
    file_ext <- tools::file_ext(file_name)

    # 创建数据记录
    data_record <- data.frame(
      文件名 = file_name,
      路径 = file_path,
      大小 = paste(file_size, "MB"),
      状态 = "已加载",
      类型 = toupper(file_ext),
      样本名称 = file_info$name,
      样本类型 = file_info$type,
      修改时间 = format(file_stat$mtime, "%Y-%m-%d %H:%M:%S"),
      stringsAsFactors = FALSE
    )

    # 获取现有数据列表并添加新记录
    existing_data <- get_data_list(project_name)
    if (is.null(existing_data) || nrow(existing_data) == 0) {
      data_list <- data_record
    } else {
      data_list <- rbind(existing_data, data_record)
    }

    # 保存更新后的数据列表
    save_data_list(data_list, project_name)

    log_info(paste("单个文件处理完成:", file_name))
    return(list(
      success = TRUE,
      message = paste("文件", file_name, "处理完成"),
      data = data_record
    ))

  }, error = function(e) {
    log_error(paste("单个文件处理失败:", e$message))
    return(list(success = FALSE, message = paste("单个文件处理失败:", e$message)))
  })
}

# 保存数据列表到项目目录
save_data_list <- function(data_list, project_name) {
  tryCatch({
    # 使用路径管理器获取数据文件路径
    data_file <- get_data_list_path()

    # 确保目录存在
    data_dir <- dirname(data_file)
    if (!dir.exists(data_dir)) {
      dir.create(data_dir, recursive = TRUE, showWarnings = FALSE)
    }

    write.csv(data_list, data_file, row.names = FALSE, fileEncoding = "UTF-8")

    log_info(paste("数据列表已保存:", data_file))
    return(TRUE)

  }, error = function(e) {
    log_error(paste("保存数据列表失败:", e$message))
    return(FALSE)
  })
}

# 处理CSV序列文件
process_csv_sequence_file <- function(csv_file, project_name = NULL) {
  tryCatch({
    log_info(paste("开始处理CSV文件:", csv_file))

    if (is.null(project_name)) {
      project_name <- get_current_project()
    }

    if (is.null(project_name)) {
      stop("无法确定项目名称，无法处理CSV文件")
    }

    # 读取CSV文件
    csv_data <- read.csv(csv_file, stringsAsFactors = FALSE, fileEncoding = "UTF-8")

    # 检查必要的列是否存在
    required_cols <- c("Sample.Type", "File.Name", "Path")
    missing_cols <- required_cols[!required_cols %in% names(csv_data)]

    if (length(missing_cols) > 0) {
      stop(paste("CSV文件缺少必要的列:", paste(missing_cols, collapse = ", ")))
    }

    # 过滤掉第一行（如果是Bracket Type行）
    if (nrow(csv_data) > 0 && grepl("Bracket Type", csv_data[1, 1], ignore.case = TRUE)) {
      csv_data <- csv_data[-1, ]
    }

    # 创建数据列表
    data_list <- data.frame(
      文件名 = character(),
      路径 = character(),
      大小 = character(),
      状态 = character(),
      类型 = character(),
      样本类型 = character(),
      修改时间 = character(),
      备注 = character(),
      stringsAsFactors = FALSE
    )

    successful_count <- 0
    failed_count <- 0

    for (i in seq_len(nrow(csv_data))) {
      tryCatch({
        row_data <- csv_data[i, ]

        # 跳过空行或无效行
        if (is.na(row_data$File.Name) || row_data$File.Name == "") {
          next
        }

        # 构建完整的文件路径
        file_name <- row_data$File.Name
        file_path <- row_data$Path
        full_path <- file.path(file_path, paste0(file_name, ".raw"))

        # 确定文件状态
        file_status <- if (file.exists(full_path)) {
          "已生成"
        } else {
          "待生成"
        }

        # 获取文件大小
        file_size <- if (file.exists(full_path)) {
          size_mb <- round(file.size(full_path) / 1024 / 1024, 2)
          paste(size_mb, "MB")
        } else {
          "未知"
        }

        # 样本类型映射
        sample_type <- switch(row_data$Sample.Type,
                             "Unknown" = "实验样本",
                             "QC" = "QC样本",
                             "Blank" = "空白样本",
                             "Standard" = "标准品",
                             row_data$Sample.Type)

        # 添加到数据列表
        new_row <- data.frame(
          文件名 = file_name,
          路径 = full_path,
          大小 = file_size,
          状态 = file_status,
          类型 = "RAW",
          样本类型 = sample_type,
          修改时间 = if (file.exists(full_path)) format(file.mtime(full_path), "%Y-%m-%d %H:%M:%S") else "",
          备注 = paste("序列位置:", row_data$Position %||% "未知"),
          stringsAsFactors = FALSE
        )

        data_list <- rbind(data_list, new_row)
        successful_count <- successful_count + 1

        log_info(paste("已处理CSV记录:", file_name))

      }, error = function(e) {
        failed_count <- failed_count + 1
        log_error(paste("处理CSV记录失败:", i, "错误:", e$message))
      })
    }

    # 保存数据列表到项目目录
    save_data_list(data_list, project_name)

    log_info(paste("CSV文件处理完成，成功:", successful_count, "失败:", failed_count))
    return(list(
      success = TRUE,
      message = paste("CSV文件处理完成，成功处理", successful_count, "条记录"),
      count = successful_count,
      data = data_list
    ))

  }, error = function(e) {
    log_error(paste("CSV文件处理失败:", e$message))
    return(list(success = FALSE, message = paste("CSV文件处理失败:", e$message)))
  })
}