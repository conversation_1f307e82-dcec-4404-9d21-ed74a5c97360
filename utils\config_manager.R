# Utils层 - 配置管理
# 负责项目配置的读取、保存和管理

# 默认配置
DEFAULT_CONFIG <- list(
  # 监控参数
  monitor = list(
    tic_enabled = TRUE,
    bpc_enabled = TRUE,
    eic_enabled = FALSE
  ),

  # 数据处理参数
  data = list(
    cache_enabled = TRUE,
    supported_formats = c(".mzML", ".mzXML", ".mgf", ".raw", ".csv")
  )
)

# 加载项目配置（从项目目录）
load_project_config <- function(project_name = NULL) {
  # 使用路径管理器获取项目配置文件路径
  config_file <- get_project_config_path()

  if (file.exists(config_file)) {
    tryCatch({
      config <- jsonlite::fromJSON(config_file)
      log_info(paste("加载项目配置:", project_name %||% "当前项目"))
      return(config)
    }, error = function(e) {
      log_warning(paste("加载项目配置失败，使用默认配置:", e$message))
      return(DEFAULT_CONFIG)
    })
  } else {
    log_info(paste("项目配置文件不存在，使用默认配置:", project_name %||% "当前项目"))
    return(DEFAULT_CONFIG)
  }
}

# 保存项目配置（到项目目录）
save_project_config <- function(config) {
  # 使用路径管理器获取项目配置文件路径
  config_file <- get_project_config_path()

  # 确保配置目录存在
  dir.create(dirname(config_file), recursive = TRUE, showWarnings = FALSE)

  tryCatch({
    jsonlite::write_json(config, config_file, pretty = TRUE, auto_unbox = TRUE)
    log_info(paste("保存项目配置:", config$name %||% "当前项目"))
    return(TRUE)
  }, error = function(e) {
    log_error(paste("保存项目配置失败:", e$message))
    return(FALSE)
  })
}

# 获取配置值
get_config_value <- function(config, key, default = NULL) {
  keys <- strsplit(key, "\\.")[[1]]
  value <- config
  
  for (k in keys) {
    if (is.list(value) && k %in% names(value)) {
      value <- value[[k]]
    } else {
      return(default)
    }
  }
  
  return(value)
}

# 设置配置值
set_config_value <- function(config, key, value) {
  keys <- strsplit(key, "\\.")[[1]]
  current <- config
  
  for (i in 1:(length(keys) - 1)) {
    k <- keys[i]
    if (!is.list(current[[k]])) {
      current[[k]] <- list()
    }
    current <- current[[k]]
  }
  
  current[[keys[length(keys)]]] <- value
  return(config)
} 