# 数据库转换器
# 将_spectra.rds文件转换为结构化数据库格式

# 加载必要的脚本和包
source("utils/path_manager.R")
source("utils/database_schema.R")
source("utils/data_extractor.R")

# 加载必要的包
load_converter_packages <- function() {
  load_database_packages()
  load_extraction_packages()
}

# 主转换函数
convert_spectra_to_database <- function(cache_dir, output_db_path) {
  cat("=== 开始Spectra数据库转换 ===\n")
  cat("缓存目录:", cache_dir, "\n")
  cat("输出数据库:", output_db_path, "\n\n")
  
  # 加载必要的包
  load_converter_packages()
  
  # 检查缓存目录
  if (!dir.exists(cache_dir)) {
    stop("缓存目录不存在:", cache_dir)
  }
  
  # 查找所有_spectra.rds文件
  rds_files <- list.files(cache_dir, pattern = "_spectra\\.rds$", full.names = TRUE)
  
  if (length(rds_files) == 0) {
    stop("在缓存目录中未找到_spectra.rds文件:", cache_dir)
  }
  
  cat("找到", length(rds_files), "个_spectra.rds文件\n\n")
  
  # 创建数据库（如果不存在）
  if (!file.exists(output_db_path)) {
    cat("创建数据库结构...\n")
    create_database(output_db_path)
  } else {
    cat("数据库已存在，将追加数据...\n")
  }
  
  # 连接到数据库
  con <- DBI::dbConnect(RSQLite::SQLite(), output_db_path)

  # 优化数据库性能设置
  DBI::dbExecute(con, "PRAGMA journal_mode = WAL")  # 写前日志模式，提高并发性能
  DBI::dbExecute(con, "PRAGMA synchronous = NORMAL")  # 平衡性能和安全性
  DBI::dbExecute(con, "PRAGMA cache_size = 10000")  # 增加缓存大小
  DBI::dbExecute(con, "PRAGMA temp_store = MEMORY")  # 临时数据存储在内存中

  tryCatch({
    # 启用外键约束
    DBI::dbExecute(con, "PRAGMA foreign_keys = ON;")

    # 处理每个文件（每个文件使用独立事务）
    successful_files <- 0
    failed_files <- 0

    for (i in seq_along(rds_files)) {
      rds_file <- rds_files[i]
      file_name <- basename(rds_file)

      cat("处理文件", i, "/", length(rds_files), ":", file_name, "\n")

      # 每个文件使用独立事务
      file_success <- tryCatch({
        DBI::dbBegin(con)

        # 提取样本类型和扫描模式
        sample_info <- extract_sample_info_from_filename(file_name)

        # 从RDS文件名提取数据索引ID
        rds_base_name <- gsub("_spectra\\.rds$", "", file_name)
        data_index_id <- if (grepl("^file_\\d{8}_\\d{6}_\\d+$", rds_base_name)) rds_base_name else NULL

        # 插入文件记录
        file_id <- insert_file_record(con, rds_file, sample_info, data_index_id)

        # 提取数据
        extracted_data <- extract_spectra_data(rds_file, file_id)

        if (!is.null(extracted_data)) {
          # 更新文件记录的统计信息
          update_file_statistics(con, file_id, extracted_data$file_info)

          # 插入MS1数据
          if (!is.null(extracted_data$ms1_data)) {
            insert_ms1_data(con, extracted_data$ms1_data, file_id)
          }

          # 插入MS2数据
          if (!is.null(extracted_data$ms2_data)) {
            insert_ms2_data(con, extracted_data$ms2_data, file_id)
          }
        }

        # 提交文件事务
        DBI::dbCommit(con)
        cat("  - 文件处理完成\n\n")
        TRUE

      }, error = function(e) {
        DBI::dbRollback(con)
        cat("  - 文件处理失败:", e$message, "\n\n")
        FALSE
      })

      if (file_success) {
        successful_files <- successful_files + 1
      } else {
        failed_files <- failed_files + 1
      }
    }

    cat("批量处理完成 - 成功:", successful_files, "个文件, 失败:", failed_files, "个文件\n")
    
    # 提交事务
    DBI::dbCommit(con)
    cat("所有数据转换完成！\n")
    
    # 生成转换报告
    generate_conversion_report(con, output_db_path)
    
  }, error = function(e) {
    # 回滚事务
    DBI::dbRollback(con)
    stop("转换过程中发生错误:", e$message)
    
  }, finally = {
    DBI::dbDisconnect(con)
  })
  
  return(TRUE)
}

# 从文件名提取样本信息
extract_sample_info_from_filename <- function(file_name) {
  # 移除.rds扩展名
  base_name <- gsub("_spectra\\.rds$", "", file_name)
  
  # 检测样本类型
  sample_type <- "SAMPLE"  # 默认
  if (grepl("QC", base_name, ignore.case = TRUE)) {
    sample_type <- "QC"
  } else if (grepl("BLANK", base_name, ignore.case = TRUE)) {
    sample_type <- "BLANK"
  } else if (grepl("STD|STAND", base_name, ignore.case = TRUE)) {
    sample_type <- "STD"
  }
  
  # 检测扫描模式
  scan_mode <- "unknown"
  if (grepl("_P_|_pos", base_name, ignore.case = TRUE)) {
    scan_mode <- "positive"
  } else if (grepl("_N_|_neg", base_name, ignore.case = TRUE)) {
    scan_mode <- "negative"
  }
  
  return(list(
    sample_type = sample_type,
    scan_mode = scan_mode
  ))
}

# 插入文件记录，使用数据索引ID作为主键
insert_file_record <- function(con, rds_file, sample_info, data_index_id = NULL) {
  file_name <- basename(rds_file)

  # 从RDS文件名提取数据索引ID
  if (is.null(data_index_id)) {
    rds_base_name <- gsub("_spectra\\.rds$", "", file_name)
    # 检查是否是数据索引ID格式
    if (grepl("^file_\\d{8}_\\d{6}_\\d+$", rds_base_name)) {
      data_index_id <- rds_base_name
    }
  }

  if (!is.null(data_index_id)) {
    # 使用数据索引ID作为主键
    insert_sql <- "
      INSERT OR REPLACE INTO data_files (file_id, file_name, file_path, sample_type, scan_mode, data_index_id)
      VALUES (?, ?, ?, ?, ?, ?)
    "

    DBI::dbExecute(con, insert_sql, list(
      data_index_id,  # 使用数据索引ID作为file_id
      file_name,
      rds_file,
      sample_info$sample_type,
      sample_info$scan_mode,
      data_index_id
    ))

    cat("  - 文件记录已插入，使用数据索引ID:", data_index_id, "\n")
    return(data_index_id)
  } else {
    # 回退到自动生成ID的方式
    insert_sql <- "
      INSERT INTO data_files (file_name, file_path, sample_type, scan_mode)
      VALUES (?, ?, ?, ?)
    "

    DBI::dbExecute(con, insert_sql, list(
      file_name,
      rds_file,
      sample_info$sample_type,
      sample_info$scan_mode
    ))

    # 获取插入的file_id
    file_id <- DBI::dbGetQuery(con, "SELECT last_insert_rowid() as id")$id

    cat("  - 文件记录已插入，file_id:", file_id, "\n")
    return(file_id)
  }
}

# 更新文件统计信息
update_file_statistics <- function(con, file_id, file_info) {
  update_sql <- "
    UPDATE data_files 
    SET total_spectra = ?, ms1_count = ?, ms2_count = ?, last_updated = CURRENT_TIMESTAMP
    WHERE file_id = ?
  "
  
  DBI::dbExecute(con, update_sql, list(
    file_info$total_spectra,
    file_info$ms1_count,
    file_info$ms2_count,
    file_id
  ))
  
  cat("  - 文件统计信息已更新\n")
}

# 插入MS1数据（并发安全版本）
insert_ms1_data <- function(con, ms1_data, file_id) {
  if (is.null(ms1_data) || nrow(ms1_data$spectra_data) == 0) {
    return()
  }

  cat("  - 插入MS1数据...\n")

  spectra_df <- ms1_data$spectra_data
  peaks_df <- ms1_data$peaks_data

  # 批量插入spectra数据并获取实际的spectrum_id
  spectrum_id_mapping <- integer(nrow(spectra_df))

  # 使用事务和批量插入提高性能
  if (nrow(spectra_df) > 0) {
    # 准备批量插入的数据
    spectra_df$file_id <- file_id  # 确保file_id正确设置

    # 批量插入spectra数据
    DBI::dbWriteTable(con, "ms1_spectra_data", spectra_df, append = TRUE, row.names = FALSE)

    # 获取插入的spectrum_id范围
    max_id <- DBI::dbGetQuery(con, "SELECT MAX(spectrum_id) as max_id FROM ms1_spectra_data")$max_id
    min_id <- max_id - nrow(spectra_df) + 1
    spectrum_id_mapping <- seq(min_id, max_id)
  }

  # 插入peaks数据
  if (nrow(peaks_df) > 0) {
    # 更新peaks数据中的spectrum_id为实际的数据库ID
    # 使用向量化操作提高性能
    peaks_df$spectrum_id <- spectrum_id_mapping[peaks_df$spectrum_id]

    # 批量插入peaks数据
    DBI::dbWriteTable(con, "ms1_peaks_data", peaks_df, append = TRUE, row.names = FALSE)
  }

  cat("    - MS1 spectraData:", nrow(spectra_df), "行\n")
  cat("    - MS1 peaksData:", nrow(peaks_df), "行\n")
}

# 插入MS2数据（并发安全版本）
insert_ms2_data <- function(con, ms2_data, file_id) {
  if (is.null(ms2_data) || nrow(ms2_data$spectra_data) == 0) {
    return()
  }

  cat("  - 插入MS2数据...\n")

  spectra_df <- ms2_data$spectra_data
  peaks_df <- ms2_data$peaks_data

  # 批量插入spectra数据并获取实际的spectrum_id
  spectrum_id_mapping <- integer(nrow(spectra_df))

  # 使用事务和批量插入提高性能
  if (nrow(spectra_df) > 0) {
    # 准备批量插入的数据
    spectra_df$file_id <- file_id  # 确保file_id正确设置

    # 批量插入spectra数据
    DBI::dbWriteTable(con, "ms2_spectra_data", spectra_df, append = TRUE, row.names = FALSE)

    # 获取插入的spectrum_id范围
    max_id <- DBI::dbGetQuery(con, "SELECT MAX(spectrum_id) as max_id FROM ms2_spectra_data")$max_id
    min_id <- max_id - nrow(spectra_df) + 1
    spectrum_id_mapping <- seq(min_id, max_id)
  }

  # 插入peaks数据
  if (nrow(peaks_df) > 0) {
    # 更新peaks数据中的spectrum_id为实际的数据库ID
    # 使用向量化操作提高性能
    peaks_df$spectrum_id <- spectrum_id_mapping[peaks_df$spectrum_id]

    # 批量插入peaks数据
    DBI::dbWriteTable(con, "ms2_peaks_data", peaks_df, append = TRUE, row.names = FALSE)
  }

  cat("    - MS2 spectraData:", nrow(spectra_df), "行\n")
  cat("    - MS2 peaksData:", nrow(peaks_df), "行\n")
}

# 并发安全的单文件处理函数（用于实时处理）
process_single_file_concurrent_safe <- function(rds_file_path, db_path) {
  cat("=== 处理单个文件（并发安全） ===\n")
  cat("文件:", rds_file_path, "\n")
  cat("数据库:", db_path, "\n")

  # 加载必要的包
  load_converter_packages()

  # 检查文件是否存在
  if (!file.exists(rds_file_path)) {
    stop("文件不存在:", rds_file_path)
  }

  # 连接到数据库
  con <- DBI::dbConnect(RSQLite::SQLite(), db_path)

  tryCatch({
    # 启用外键约束
    DBI::dbExecute(con, "PRAGMA foreign_keys = ON;")

    # 开始事务
    DBI::dbBegin(con)

    file_name <- basename(rds_file_path)

    # 检查文件是否已经存在于数据库中
    existing_file <- DBI::dbGetQuery(con,
      "SELECT file_id FROM data_files WHERE file_name = ?",
      list(file_name))

    if (nrow(existing_file) > 0) {
      DBI::dbRollback(con)
      cat("文件已存在于数据库中，跳过:", file_name, "\n")
      return(list(success = TRUE, message = "文件已存在", file_id = existing_file$file_id[1]))
    }

    # 提取样本类型和扫描模式
    sample_info <- extract_sample_info_from_filename(file_name)

    # 从RDS文件名提取数据索引ID
    rds_base_name <- gsub("_spectra\\.rds$", "", file_name)
    data_index_id <- if (grepl("^file_\\d{8}_\\d{6}_\\d+$", rds_base_name)) rds_base_name else NULL

    # 插入文件记录
    file_id <- insert_file_record(con, rds_file_path, sample_info, data_index_id)

    # 提取数据
    extracted_data <- extract_spectra_data(rds_file_path, file_id)

    if (!is.null(extracted_data)) {
      # 更新文件记录的统计信息
      update_file_statistics(con, file_id, extracted_data$file_info)

      # 插入MS1数据
      if (!is.null(extracted_data$ms1_data)) {
        insert_ms1_data(con, extracted_data$ms1_data, file_id)
      }

      # 插入MS2数据
      if (!is.null(extracted_data$ms2_data)) {
        insert_ms2_data(con, extracted_data$ms2_data, file_id)
      }

      # 提交事务
      DBI::dbCommit(con)
      cat("文件处理完成:", file_name, "\n")
      return(list(success = TRUE, file_id = file_id))

    } else {
      DBI::dbRollback(con)
      return(list(success = FALSE, error = "数据提取失败"))
    }

  }, error = function(e) {
    DBI::dbRollback(con)
    cat("文件处理失败:", e$message, "\n")
    return(list(success = FALSE, error = e$message))
  }, finally = {
    DBI::dbDisconnect(con)
  })
}

# 生成转换报告
generate_conversion_report <- function(con, db_path) {
  cat("\n=== 转换报告 ===\n")
  
  # 获取数据库信息
  db_info <- get_database_info(db_path)
  
  cat("数据库文件:", db_path, "\n")
  cat("数据库大小:", round(file.size(db_path) / 1024 / 1024, 2), "MB\n")
  cat("表统计:\n")
  
  for (table_name in names(db_info$table_counts)) {
    count <- db_info$table_counts[[table_name]]
    cat("  -", table_name, ":", count, "行\n")
  }
  
  # 获取样本类型统计
  sample_stats <- DBI::dbGetQuery(con, "
    SELECT sample_type, scan_mode, COUNT(*) as count 
    FROM data_files 
    GROUP BY sample_type, scan_mode
    ORDER BY sample_type, scan_mode
  ")
  
  if (nrow(sample_stats) > 0) {
    cat("\n样本统计:\n")
    for (i in 1:nrow(sample_stats)) {
      row <- sample_stats[i, ]
      cat("  -", row$sample_type, "(", row$scan_mode, "):", row$count, "个文件\n")
    }
  }
  
  cat("\n转换完成！\n")
}

# 主函数：转换项目中的所有数据
convert_project_spectra <- function(project_root = NULL) {
  # 使用路径管理服务获取当前项目路径
  if (is.null(project_root)) {
    project_root <- get_project_root_path()
    if (is.null(project_root)) {
      stop("无法获取项目路径，请确保项目已正确设置")
    }
  }

  # 使用路径管理服务构建项目相对路径
  cache_dir <- get_project_absolute_path("data", "cache", "spectra_v2")
  results_dir <- get_project_absolute_path("results")

  # 确保results目录存在
  if (!validate_path(results_dir, create_if_missing = TRUE)) {
    stop("无法创建结果目录:", results_dir)
  }

  # 构建数据库文件路径
  output_db_path <- file.path(results_dir, "spectra.db")

  cat("项目根目录:", project_root, "\n")
  cat("缓存目录:", cache_dir, "\n")
  cat("输出数据库:", output_db_path, "\n\n")

  # 执行转换
  result <- convert_spectra_to_database(cache_dir, output_db_path)

  return(list(
    success = result,
    database_path = output_db_path
  ))
}
