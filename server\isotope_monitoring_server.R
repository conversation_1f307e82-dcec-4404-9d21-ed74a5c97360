# Server层 - 同位素内标监控服务器逻辑
# 负责同位素内标监控的数据提取、分析和可视化

# 检查并加载必要的包
required_packages <- c("DBI", "RSQLite", "dplyr", "plotly", "yaml", "stats", "digest", "DT")
for (pkg in required_packages) {
  if (!requireNamespace(pkg, quietly = TRUE)) {
    install.packages(pkg)
  }
}

library(DBI)
library(RSQLite)
library(dplyr)
library(plotly)
library(yaml)
library(stats)
library(digest)
library(DT)

# 同位素内标数据提取器
IsotopeInternalStandardExtractor <- setRefClass("IsotopeInternalStandardExtractor",
  fields = list(
    db_path = "character",
    config_data = "list",
    mass_tolerance_ppm = "numeric"
  ),
  methods = list(
    # 初始化
    initialize = function(database_path, config_path = NULL, tolerance_ppm = 10) {
      db_path <<- database_path
      mass_tolerance_ppm <<- tolerance_ppm
      config_data <<- list()
      
      if (!is.null(config_path) && file.exists(config_path)) {
        load_config(config_path)
      }
    },
    
    # 加载YAML配置文件
    load_config = function(config_path) {
      tryCatch({
        config_data <<- yaml::read_yaml(config_path, fileEncoding = "UTF-8")
        log_info(paste("已加载同位素内标配置:", config_path))
        return(TRUE)
      }, error = function(e) {
        log_error(paste("加载配置文件失败:", e$message))
        return(FALSE)
      })
    },
    
    # 从监控离子管理器加载配置
    load_config_from_monitor_ions = function() {
      tryCatch({
        # 尝试从项目中加载YAML格式的监控离子数据
        project_root <- get_project_root_path()
        if (!is.null(project_root)) {
          yaml_path <- file.path(project_root, "data", "monitor_ions_data.yaml")
          if (file.exists(yaml_path)) {
            yaml_data <- yaml::read_yaml(yaml_path, fileEncoding = "UTF-8")
            if (!is.null(yaml_data$monitor_ions_data) && length(yaml_data$monitor_ions_data) > 0) {
              # 转换新格式为内部格式
              converted_compounds <- convert_yaml_to_internal_format(yaml_data$monitor_ions_data)
              config_data <<- list(monitor_ions = converted_compounds)
              log_info(paste("已从YAML文件加载", length(converted_compounds), "个同位素内标配置"))
              return(TRUE)
            }
          }
        }

        # 如果YAML文件不存在，尝试从监控离子管理器加载
        if (exists("monitor_ions_config")) {
          compounds <- monitor_ions_config$get_all_compounds()
          if (length(compounds) > 0) {
            config_data <<- list(monitor_ions = compounds)
            log_info("已从监控离子管理器加载配置")
            return(TRUE)
          }
        }

        log_warning("监控离子管理器中没有配置数据")
        return(FALSE)
      }, error = function(e) {
        log_error(paste("从监控离子管理器加载配置失败:", e$message))
        return(FALSE)
      })
    },

    # 将新格式YAML数据转换为内部格式
    convert_yaml_to_internal_format = function(yaml_ions_data) {
      compounds_map <- list()

      for (ion_data in yaml_ions_data) {
        compound_name <- ion_data$化合物名称
        if (is.null(compound_name) || compound_name == "") next

        # 如果化合物不存在，创建新的化合物配置
        if (is.null(compounds_map[[compound_name]])) {
          compounds_map[[compound_name]] <- list(
            compound_name = compound_name,
            molecular_weight = ion_data$分子质量 %||% 0,
            retention_time = ion_data$保留时间 %||% 0,
            scan_mode = ion_data$扫描模式 %||% "DDA",
            ionization_mode = ion_data$离子化模式 %||% "positive",
            monitor_ions = list()
          )
        }

        # 添加监控离子
        ion_config <- list(
          mz = ion_data$离子质荷比 %||% 0,
          ion_type = ion_data$离子类型 %||% "[M+H]+",
          ms_level = if (grepl("MS2", ion_data$MS级别 %||% "MS1")) 2 else 1,
          notes = ion_data$备注 %||% ""
        )

        compounds_map[[compound_name]]$monitor_ions <- c(
          compounds_map[[compound_name]]$monitor_ions,
          list(ion_config)
        )
      }

      # 转换为列表格式
      return(unname(compounds_map))
    },
    
    # 计算质量容差范围
    calculate_mass_tolerance = function(target_mz, ppm_tolerance = NULL) {
      if (is.null(ppm_tolerance)) {
        ppm_tolerance <- mass_tolerance_ppm
      }
      
      delta_mz <- target_mz * ppm_tolerance / 1e6
      return(list(
        min_mz = target_mz - delta_mz,
        max_mz = target_mz + delta_mz,
        delta_mz = delta_mz
      ))
    },
    
    # 提取单个化合物的MS1数据
    extract_compound_ms1_data = function(compound_config, file_ids = NULL) {
      if (is.null(compound_config$monitor_ions) || length(compound_config$monitor_ions) == 0) {
        return(NULL)
      }
      
      con <- DBI::dbConnect(RSQLite::SQLite(), db_path)
      on.exit(DBI::dbDisconnect(con))
      
      all_compound_data <- list()
      
      for (ion in compound_config$monitor_ions) {
        if (is.null(ion$mz) || ion$mz <= 0) next
        
        # 计算质量容差
        mass_range <- calculate_mass_tolerance(ion$mz)
        
        # 构建查询条件
        file_condition <- ""
        if (!is.null(file_ids) && length(file_ids) > 0) {
          file_ids_str <- paste0("'", file_ids, "'", collapse = ",")
          file_condition <- paste0("AND ms1.file_id IN (", file_ids_str, ")")
        }
        
        # 保留时间窗口
        rt_condition <- ""
        if (!is.null(compound_config$retention_time) && compound_config$retention_time > 0) {
          rt_window <- 0.5  # 默认±0.5分钟窗口
          if (!is.null(compound_config$retention_time_window)) {
            rt_window <- compound_config$retention_time_window
          }
          rt_min <- compound_config$retention_time - rt_window
          rt_max <- compound_config$retention_time + rt_window
          rt_condition <- paste0("AND ms1.rtime BETWEEN ", rt_min, " AND ", rt_max)
        }
        
        # SQL查询
        query <- paste0("
          SELECT 
            df.file_name,
            df.file_id,
            df.sample_type,
            ms1.spectrum_id,
            ms1.scan_index,
            ms1.rtime,
            ms1.polarity,
            ms1.tot_ion_current,
            ms1.base_peak_mz,
            ms1.base_peak_intensity,
            peaks.mz,
            peaks.intensity,
            '", compound_config$compound_name, "' as compound_name,
            ", ion$mz, " as target_mz,
            '", ion$ion_type %||% '[M+H]+', "' as ion_type
          FROM data_files df
          JOIN ms1_spectra_data ms1 ON df.file_id = ms1.file_id
          JOIN ms1_peaks_data peaks ON ms1.spectrum_id = peaks.spectrum_id
          WHERE peaks.mz BETWEEN ", mass_range$min_mz, " AND ", mass_range$max_mz, "
          ", file_condition, "
          ", rt_condition, "
          ORDER BY df.file_name, ms1.rtime, peaks.intensity DESC
        ")
        
        result <- DBI::dbGetQuery(con, query)

        # 安全检查查询结果
        if (!is.null(result) && is.data.frame(result) &&
            !is.na(nrow(result)) && nrow(result) > 0) {
          # 计算m/z偏差
          result$mz_deviation_ppm <- ((result$mz - result$target_mz) / result$target_mz) * 1e6
          result$mz_deviation_abs <- abs(result$mz_deviation_ppm)

          all_compound_data[[length(all_compound_data) + 1]] <- result
        }
      }
      
      if (length(all_compound_data) > 0) {
        return(do.call(rbind, all_compound_data))
      } else {
        return(NULL)
      }
    },
    
    # 提取所有配置化合物的数据
    extract_all_compounds_data = function(file_ids = NULL) {
      if (length(config_data) == 0 || is.null(config_data$monitor_ions)) {
        log_warning("没有配置数据可用于提取")
        return(NULL)
      }
      
      all_data <- list()
      
      for (compound in config_data$monitor_ions) {
        compound_data <- extract_compound_ms1_data(compound, file_ids)
        if (!is.null(compound_data)) {
          all_data[[length(all_data) + 1]] <- compound_data
        }
      }
      
      if (length(all_data) > 0) {
        combined_data <- do.call(rbind, all_data)
        log_info(paste("提取了", nrow(combined_data), "条同位素内标数据记录"))
        return(combined_data)
      } else {
        log_warning("没有提取到任何同位素内标数据")
        return(NULL)
      }
    }
  )
)

# MS2-MS1匹配功能
MS2MS1Matcher <- setRefClass("MS2MS1Matcher",
  fields = list(
    db_path = "character"
  ),
  methods = list(
    initialize = function(database_path) {
      db_path <<- database_path
    },
    
    # 查找MS1扫描对应的MS2数据
    find_ms2_for_ms1 = function(file_id, scan_index) {
      con <- DBI::dbConnect(RSQLite::SQLite(), db_path)
      on.exit(DBI::dbDisconnect(con))
      
      query <- paste0("
        SELECT 
          ms2.spectrum_id,
          ms2.scan_index as ms2_scan_index,
          ms2.rtime as ms2_rtime,
          ms2.precursor_mz,
          ms2.precursor_intensity,
          ms2.precursor_charge,
          ms2.collision_energy,
          ms2.prec_scan_num
        FROM ms2_spectra_data ms2
        WHERE ms2.file_id = '", file_id, "'
        AND ms2.prec_scan_num = ", scan_index, "
        ORDER BY ms2.rtime
      ")
      
      result <- DBI::dbGetQuery(con, query)
      return(result)
    },
    
    # 批量匹配MS2数据
    batch_match_ms2 = function(ms1_data) {
      if (is.null(ms1_data) || !is.data.frame(ms1_data) ||
          is.na(nrow(ms1_data)) || nrow(ms1_data) == 0) {
        return(ms1_data)
      }
      
      # 添加MS2匹配标志列
      ms1_data$has_ms2 <- FALSE
      ms1_data$ms2_count <- 0
      ms1_data$ms2_spectrum_ids <- ""
      
      # 按文件分组处理
      for (file_id in unique(ms1_data$file_id)) {
        file_data <- ms1_data[ms1_data$file_id == file_id, ]
        
        for (i in 1:nrow(file_data)) {
          scan_index <- file_data$scan_index[i]
          ms2_matches <- find_ms2_for_ms1(file_id, scan_index)
          
          if (nrow(ms2_matches) > 0) {
            row_idx <- which(ms1_data$file_id == file_id & ms1_data$scan_index == scan_index)
            ms1_data$has_ms2[row_idx] <- TRUE
            ms1_data$ms2_count[row_idx] <- nrow(ms2_matches)
            ms1_data$ms2_spectrum_ids[row_idx] <- paste(ms2_matches$spectrum_id, collapse = ",")
          }
        }
      }
      
      return(ms1_data)
    },
    
    # 获取MS2谱图数据
    get_ms2_spectrum_data = function(spectrum_id) {
      con <- DBI::dbConnect(RSQLite::SQLite(), db_path)
      on.exit(DBI::dbDisconnect(con))
      
      # 获取MS2谱图元数据
      meta_query <- paste0("
        SELECT 
          ms2.*,
          df.file_name
        FROM ms2_spectra_data ms2
        JOIN data_files df ON ms2.file_id = df.file_id
        WHERE ms2.spectrum_id = ", spectrum_id)
      
      meta_data <- DBI::dbGetQuery(con, meta_query)
      
      # 获取MS2峰数据
      peaks_query <- paste0("
        SELECT mz, intensity
        FROM ms2_peaks_data
        WHERE spectrum_id = ", spectrum_id, "
        ORDER BY mz")
      
      peaks_data <- DBI::dbGetQuery(con, peaks_query)
      
      return(list(
        metadata = meta_data,
        peaks = peaks_data
      ))
    }
  )
)

# 峰形分析器
PeakShapeAnalyzer <- setRefClass("PeakShapeAnalyzer",
  methods = list(
    # 高斯拟合函数
    fit_gaussian = function(x, y, min_points = 5) {
      if (length(x) < min_points || length(y) < min_points) {
        return(NULL)
      }

      tryCatch({
        # 移除强度为0或负值的点
        valid_idx <- y > 0 & !is.na(y) & !is.na(x)
        if (sum(valid_idx) < min_points) {
          return(NULL)
        }

        x_clean <- x[valid_idx]
        y_clean <- y[valid_idx]

        # 初始参数估计
        max_idx <- which.max(y_clean)
        a_init <- max(y_clean)  # 峰高
        b_init <- x_clean[max_idx]  # 峰位置
        c_init <- (max(x_clean) - min(x_clean)) / 4  # 峰宽估计

        # 高斯函数
        gaussian_func <- function(x, a, b, c) {
          a * exp(-((x - b)^2) / (2 * c^2))
        }

        # 非线性拟合
        fit <- nls(y_clean ~ gaussian_func(x_clean, a, b, c),
                   start = list(a = a_init, b = b_init, c = c_init),
                   control = nls.control(maxiter = 100, warnOnly = TRUE))

        # 提取参数
        params <- coef(fit)
        a <- params["a"]
        b <- params["b"]
        c <- abs(params["c"])  # 确保c为正值

        # 计算拟合质量
        fitted_values <- predict(fit)
        r_squared <- 1 - sum((y_clean - fitted_values)^2) / sum((y_clean - mean(y_clean))^2)

        # 计算峰参数
        fwhm <- 2 * sqrt(2 * log(2)) * c  # 半峰宽
        peak_area <- a * c * sqrt(2 * pi)  # 峰面积

        return(list(
          amplitude = a,
          center = b,
          sigma = c,
          fwhm = fwhm,
          area = peak_area,
          r_squared = r_squared,
          fitted_values = fitted_values,
          success = TRUE
        ))

      }, error = function(e) {
        return(list(success = FALSE, error = e$message))
      })
    },

    # 计算信噪比
    calculate_snr = function(intensities, noise_percentile = 5) {
      if (length(intensities) == 0) return(0)

      # 使用低百分位数估计噪音水平
      noise_level <- quantile(intensities, noise_percentile / 100, na.rm = TRUE)
      signal_level <- max(intensities, na.rm = TRUE)

      if (noise_level <= 0) noise_level <- min(intensities[intensities > 0], na.rm = TRUE)

      snr <- signal_level / noise_level
      return(snr)
    },

    # 识别非噪音点
    identify_non_noise_points = function(intensities, noise_threshold_factor = 3) {
      if (length(intensities) == 0) return(logical(0))

      # 计算噪音阈值
      noise_level <- quantile(intensities, 0.1, na.rm = TRUE)  # 使用10%分位数作为噪音水平
      threshold <- noise_level * noise_threshold_factor

      return(intensities > threshold)
    },

    # 分析单个化合物在单个样本中的峰形
    analyze_compound_peak = function(compound_data) {
      if (is.null(compound_data) || !is.data.frame(compound_data) ||
          is.na(nrow(compound_data)) || nrow(compound_data) == 0) {
        return(NULL)
      }

      # 按保留时间排序
      compound_data <- compound_data[order(compound_data$rtime), ]

      # 识别非噪音点
      non_noise <- identify_non_noise_points(compound_data$intensity)

      if (sum(non_noise) < 3) {
        return(list(
          success = FALSE,
          reason = "insufficient_non_noise_points",
          total_points = nrow(compound_data),
          non_noise_points = sum(non_noise)
        ))
      }

      # 使用非噪音点进行分析
      clean_data <- compound_data[non_noise, ]

      # 找到最高强度点
      max_intensity_idx <- which.max(clean_data$intensity)
      peak_rt <- clean_data$rtime[max_intensity_idx]
      peak_intensity <- clean_data$intensity[max_intensity_idx]
      peak_mz <- clean_data$mz[max_intensity_idx]

      # 计算m/z偏差（使用最高强度点）
      mz_deviation_ppm <- clean_data$mz_deviation_ppm[max_intensity_idx]

      # 计算m/z扫描范围
      mz_range <- list(
        min_mz = min(clean_data$mz),
        max_mz = max(clean_data$mz),
        range_width = max(clean_data$mz) - min(clean_data$mz)
      )

      # 高斯拟合
      gaussian_fit <- fit_gaussian(clean_data$rtime, clean_data$intensity)

      # 计算信噪比
      snr <- calculate_snr(compound_data$intensity)

      result <- list(
        success = TRUE,
        compound_name = clean_data$compound_name[1],
        file_name = clean_data$file_name[1],
        retention_time = peak_rt,
        peak_intensity = peak_intensity,
        peak_mz = peak_mz,
        mz_deviation_ppm = mz_deviation_ppm,
        mz_range = mz_range,
        snr = snr,
        total_points = nrow(compound_data),
        non_noise_points = nrow(clean_data),
        gaussian_fit = gaussian_fit
      )

      # 如果高斯拟合成功，添加峰形参数
      if (!is.null(gaussian_fit) && gaussian_fit$success) {
        result$fwhm <- gaussian_fit$fwhm
        result$peak_area <- gaussian_fit$area
        result$peak_shape_quality <- gaussian_fit$r_squared
        result$fitted_center <- gaussian_fit$center
      } else {
        result$fwhm <- NA
        result$peak_area <- NA
        result$peak_shape_quality <- NA
        result$fitted_center <- NA
      }

      return(result)
    }
  )
)

# 数据质量评估器
DataQualityAssessor <- setRefClass("DataQualityAssessor",
  methods = list(
    # 识别异常值
    identify_outliers = function(values, method = "iqr", factor = 1.5) {
      if (length(values) < 3) return(rep(FALSE, length(values)))

      if (method == "iqr") {
        Q1 <- quantile(values, 0.25, na.rm = TRUE)
        Q3 <- quantile(values, 0.75, na.rm = TRUE)
        IQR <- Q3 - Q1

        lower_bound <- Q1 - factor * IQR
        upper_bound <- Q3 + factor * IQR

        return(values < lower_bound | values > upper_bound)
      } else if (method == "zscore") {
        z_scores <- abs((values - mean(values, na.rm = TRUE)) / sd(values, na.rm = TRUE))
        return(z_scores > factor)
      }

      return(rep(FALSE, length(values)))
    },

    # 评估数据质量
    assess_quality = function(peak_analysis_results) {
      if (is.null(peak_analysis_results) || length(peak_analysis_results) == 0) {
        return(NULL)
      }

      # 提取关键指标
      rt_values <- sapply(peak_analysis_results, function(x) x$retention_time %||% NA)
      intensity_values <- sapply(peak_analysis_results, function(x) x$peak_intensity %||% NA)
      mz_dev_values <- sapply(peak_analysis_results, function(x) abs(x$mz_deviation_ppm) %||% NA)
      snr_values <- sapply(peak_analysis_results, function(x) x$snr %||% NA)
      fwhm_values <- sapply(peak_analysis_results, function(x) x$fwhm %||% NA)

      # 识别异常值
      rt_outliers <- identify_outliers(rt_values[!is.na(rt_values)])
      intensity_outliers <- identify_outliers(log10(intensity_values[!is.na(intensity_values)]))
      mz_dev_outliers <- identify_outliers(mz_dev_values[!is.na(mz_dev_values)])
      snr_outliers <- identify_outliers(log10(snr_values[!is.na(snr_values)]))
      fwhm_outliers <- identify_outliers(fwhm_values[!is.na(fwhm_values)])

      # 为每个结果添加质量标志
      for (i in seq_along(peak_analysis_results)) {
        result <- peak_analysis_results[[i]]
        quality_flags <- list()

        # 检查保留时间异常
        if (!is.na(rt_values[i]) && i <= length(rt_outliers) && rt_outliers[i]) {
          quality_flags$retention_time_outlier <- TRUE
        }

        # 检查强度异常
        if (!is.na(intensity_values[i]) && i <= length(intensity_outliers) && intensity_outliers[i]) {
          quality_flags$intensity_outlier <- TRUE
        }

        # 检查m/z偏差异常
        if (!is.na(mz_dev_values[i]) && i <= length(mz_dev_outliers) && mz_dev_outliers[i]) {
          quality_flags$mz_deviation_outlier <- TRUE
        }

        # 检查信噪比异常
        if (!is.na(snr_values[i]) && i <= length(snr_outliers) && snr_outliers[i]) {
          quality_flags$snr_outlier <- TRUE
        }

        # 检查峰宽异常
        if (!is.na(fwhm_values[i]) && i <= length(fwhm_outliers) && fwhm_outliers[i]) {
          quality_flags$fwhm_outlier <- TRUE
        }

        # 检查峰形质量
        if (!is.na(result$peak_shape_quality) && result$peak_shape_quality < 0.8) {
          quality_flags$poor_peak_shape <- TRUE
        }

        # 检查信噪比阈值
        if (!is.na(result$snr) && result$snr < 10) {
          quality_flags$low_snr <- TRUE
        }

        result$quality_flags <- quality_flags
        result$has_quality_issues <- length(quality_flags) > 0

        peak_analysis_results[[i]] <- result
      }

      return(peak_analysis_results)
    }
  )
)

# 可视化生成器
IsotopeVisualizationGenerator <- setRefClass("IsotopeVisualizationGenerator",
  methods = list(
    # 创建3D散点图
    create_3d_scatter_plot = function(isotope_data, ms2_matched_data = NULL) {
      if (is.null(isotope_data) || !is.data.frame(isotope_data) ||
          is.na(nrow(isotope_data)) || nrow(isotope_data) == 0) {
        return(NULL)
      }

      # 准备数据
      plot_data <- isotope_data

      # 添加MS2匹配信息
      if (!is.null(ms2_matched_data)) {
        plot_data <- ms2_matched_data
      }

      # 计算m/z偏差（如果还没有计算）
      if (!"mz_deviation_ppm" %in% colnames(plot_data)) {
        plot_data$mz_deviation_ppm <- ((plot_data$mz - plot_data$target_mz) / plot_data$target_mz) * 1e6
      }

      # 为每个样本分配Y轴位置（数值型，便于3D显示）
      unique_samples <- unique(plot_data$file_name)
      sample_positions <- data.frame(
        file_name = unique_samples,
        y_position = seq_along(unique_samples),
        stringsAsFactors = FALSE
      )
      plot_data <- merge(plot_data, sample_positions, by = "file_name", all.x = TRUE)

      # 创建颜色映射（基于m/z偏差绝对值）
      plot_data$color_value <- abs(plot_data$mz_deviation_ppm)

      # 创建形状映射（基于MS2存在性）
      if ("has_ms2" %in% colnames(plot_data)) {
        plot_data$shape_symbol <- ifelse(plot_data$has_ms2 %||% FALSE, "circle", "triangle-up")
        plot_data$ms2_text <- ifelse(plot_data$has_ms2 %||% FALSE, "有MS2", "无MS2")
      } else {
        plot_data$shape_symbol <- "circle"
        plot_data$ms2_text <- "未知"
      }

      # 创建详细的悬停文本
      plot_data$hover_text <- paste0(
        "<b>", plot_data$compound_name, "</b><br>",
        "样本: ", plot_data$file_name, "<br>",
        "保留时间: ", round(plot_data$rtime, 3), " min<br>",
        "强度: ", format(plot_data$intensity, big.mark = ",", scientific = FALSE), "<br>",
        "实测m/z: ", round(plot_data$mz, 4), "<br>",
        "理论m/z: ", round(plot_data$target_mz, 4), "<br>",
        "m/z偏差: ", round(plot_data$mz_deviation_ppm, 2), " ppm<br>",
        "MS2状态: ", plot_data$ms2_text, "<br>",
        "离子类型: ", plot_data$ion_type %||% "[M+H]+"
      )

      # 创建3D散点图
      p <- plot_ly(
        data = plot_data,
        x = ~rtime,
        y = ~y_position,
        z = ~intensity,
        color = ~color_value,
        colors = c("blue", "green", "yellow", "orange", "red"),
        symbol = ~shape_symbol,
        symbols = c("circle", "triangle-up"),
        type = "scatter3d",
        mode = "markers",
        marker = list(
          size = 6,
          line = list(width = 1, color = "rgba(0,0,0,0.3)")
        ),
        text = ~hover_text,
        hovertemplate = "%{text}<extra></extra>"
      ) %>%
      layout(
        title = list(
          text = "同位素内标MS1三维散点图",
          font = list(size = 16)
        ),
        scene = list(
          xaxis = list(
            title = "保留时间 (min)",
            titlefont = list(size = 14),
            tickfont = list(size = 12)
          ),
          yaxis = list(
            title = "样本名称",
            titlefont = list(size = 14),
            tickfont = list(size = 10),
            tickmode = "array",
            tickvals = sample_positions$y_position,
            ticktext = sample_positions$file_name
          ),
          zaxis = list(
            title = "强度",
            titlefont = list(size = 14),
            tickfont = list(size = 12),
            type = "log"
          ),
          camera = list(
            eye = list(x = 1.5, y = 1.5, z = 1.5)
          )
        ),
        margin = list(l = 0, r = 0, b = 0, t = 40),
        showlegend = TRUE
      ) %>%
      colorbar(
        title = "m/z偏差 (ppm)",
        titleside = "right"
      )

      return(p)
    },

    # 创建MS2谱图
    create_ms2_spectrum_plot = function(ms2_spectrum_data) {
      if (is.null(ms2_spectrum_data) || is.null(ms2_spectrum_data$peaks) ||
          !is.data.frame(ms2_spectrum_data$peaks) ||
          is.na(nrow(ms2_spectrum_data$peaks)) || nrow(ms2_spectrum_data$peaks) == 0) {
        return(NULL)
      }

      peaks <- ms2_spectrum_data$peaks
      metadata <- ms2_spectrum_data$metadata

      # 计算相对强度（基于基峰归一化）
      max_intensity <- max(peaks$intensity, na.rm = TRUE)
      peaks$relative_intensity <- (peaks$intensity / max_intensity) * 100

      # 识别重要峰（相对强度 > 5%）
      important_peaks <- peaks$relative_intensity > 5
      peaks$peak_importance <- ifelse(important_peaks, "重要峰", "次要峰")

      # 创建颜色映射
      peak_colors <- ifelse(important_peaks, "#2E86AB", "#A23B72")

      # 创建详细的悬停文本
      peaks$hover_text <- paste0(
        "<b>m/z: ", round(peaks$mz, 4), "</b><br>",
        "绝对强度: ", format(peaks$intensity, big.mark = ",", scientific = FALSE), "<br>",
        "相对强度: ", round(peaks$relative_intensity, 1), "%<br>",
        "峰类型: ", peaks$peak_importance
      )

      # 创建MS2谱图
      p <- plot_ly(
        data = peaks,
        x = ~mz,
        y = ~intensity,
        type = "bar",
        marker = list(
          color = peak_colors,
          line = list(width = 0.5, color = "black")
        ),
        text = ~hover_text,
        hovertemplate = "%{text}<extra></extra>",
        name = "MS2峰"
      ) %>%
      layout(
        title = list(
          text = paste0("MS2碎片离子谱图 - ", metadata$file_name[1]),
          font = list(size = 16)
        ),
        xaxis = list(
          title = "m/z",
          titlefont = list(size = 14),
          tickfont = list(size = 12),
          showgrid = TRUE,
          gridcolor = "rgba(128,128,128,0.2)"
        ),
        yaxis = list(
          title = "强度",
          titlefont = list(size = 14),
          tickfont = list(size = 12),
          showgrid = TRUE,
          gridcolor = "rgba(128,128,128,0.2)"
        ),
        plot_bgcolor = "rgba(240,240,240,0.3)",
        annotations = list(
          list(
            x = 0.02, y = 0.98,
            xref = "paper", yref = "paper",
            text = paste0(
              "<b>前体离子信息</b><br>",
              "m/z: ", round(metadata$precursor_mz[1], 4), "<br>",
              "强度: ", format(metadata$precursor_intensity[1], big.mark = ",", scientific = FALSE), "<br>",
              "电荷: ", metadata$precursor_charge[1] %||% "未知", "<br>",
              "碰撞能: ", metadata$collision_energy[1], " eV<br>",
              "保留时间: ", round(metadata$rtime[1], 3), " min<br>",
              "扫描编号: ", metadata$scan_index[1]
            ),
            showarrow = FALSE,
            align = "left",
            bgcolor = "rgba(255,255,255,0.9)",
            bordercolor = "#2E86AB",
            borderwidth = 2,
            font = list(size = 11)
          ),
          list(
            x = 0.98, y = 0.98,
            xref = "paper", yref = "paper",
            text = paste0(
              "<b>谱图统计</b><br>",
              "总峰数: ", nrow(peaks), "<br>",
              "重要峰数: ", sum(important_peaks), "<br>",
              "基峰m/z: ", round(peaks$mz[which.max(peaks$intensity)], 4), "<br>",
              "基峰强度: ", format(max_intensity, big.mark = ",", scientific = FALSE)
            ),
            showarrow = FALSE,
            align = "right",
            bgcolor = "rgba(255,255,255,0.9)",
            bordercolor = "#A23B72",
            borderwidth = 2,
            font = list(size = 11)
          )
        ),
        margin = list(l = 60, r = 60, b = 60, t = 80),
        showlegend = FALSE
      )

      return(p)
    }
  )
)

# 空值合并操作符（如果不存在）
if (!exists("%||%")) {
  `%||%` <- function(x, y) {
    if (is.null(x) || length(x) == 0 || (is.character(x) && x == "")) y else x
  }
}

# 主要处理函数
IsotopeMonitoringProcessor <- setRefClass("IsotopeMonitoringProcessor",
  fields = list(
    extractor = "ANY",
    matcher = "ANY",
    analyzer = "ANY",
    assessor = "ANY",
    visualizer = "ANY"
  ),
  methods = list(
    # 初始化
    initialize = function(database_path, config_path = NULL, tolerance_ppm = 10) {
      extractor <<- IsotopeInternalStandardExtractor$new(database_path, config_path, tolerance_ppm)
      matcher <<- MS2MS1Matcher$new(database_path)
      analyzer <<- PeakShapeAnalyzer$new()
      assessor <<- DataQualityAssessor$new()
      visualizer <<- IsotopeVisualizationGenerator$new()
    },

    # 完整的同位素内标分析流程（带缓存）
    process_isotope_monitoring = function(file_ids = NULL, include_ms2_matching = TRUE, use_cache = TRUE) {
      tryCatch({
        log_info("开始同位素内标监控分析...")

        # 1. 从监控离子管理器加载配置
        if (!extractor$load_config_from_monitor_ions()) {
          stop("无法加载监控离子配置")
        }

        # 2. 检查缓存（如果启用）
        cache_key <- NULL
        if (use_cache && !is.null(isotope_cache)) {
          config_hash <- digest::digest(extractor$config_data, algo = "md5")
          cache_key <- isotope_cache$generate_cache_key(file_ids %||% "all", config_hash, extractor$mass_tolerance_ppm)

          cached_results <- isotope_cache$load_from_cache(cache_key)
          if (!is.null(cached_results)) {
            log_info("使用缓存的分析结果")
            return(cached_results)
          }
        }

        # 3. 提取同位素内标数据
        log_info("提取同位素内标数据...")
        isotope_data <- extractor$extract_all_compounds_data(file_ids)

        # 安全检查数据有效性
        if (is.null(isotope_data) || !is.data.frame(isotope_data) ||
            is.na(nrow(isotope_data)) || nrow(isotope_data) == 0) {
          return(list(
            success = FALSE,
            message = "没有找到同位素内标数据",
            data = NULL
          ))
        }

        # 4. MS2-MS1匹配（如果需要）
        matched_data <- isotope_data
        if (include_ms2_matching) {
          log_info("执行MS2-MS1匹配...")
          matched_data <- matcher$batch_match_ms2(isotope_data)
        }

        # 5. 峰形分析
        log_info("执行峰形分析...")
        peak_analysis_results <- analyze_peaks_by_compound_and_file(matched_data)

        # 6. 数据质量评估
        log_info("执行数据质量评估...")
        quality_assessed_results <- assessor$assess_quality(peak_analysis_results)

        # 7. 生成可视化
        log_info("生成可视化图表...")
        scatter_plot <- visualizer$create_3d_scatter_plot(matched_data, matched_data)

        # 8. 生成信息表格
        info_table <- create_isotope_info_table(quality_assessed_results)

        log_info("同位素内标监控分析完成")

        results <- list(
          success = TRUE,
          message = "分析完成",
          data = list(
            raw_data = matched_data,
            peak_analysis = quality_assessed_results,
            info_table = info_table,
            scatter_plot = scatter_plot,
            summary = list(
              total_compounds = length(unique(matched_data$compound_name)),
              total_samples = length(unique(matched_data$file_name)),
              total_data_points = nrow(matched_data),
              ms2_matched_points = if(include_ms2_matching) sum(matched_data$has_ms2, na.rm = TRUE) else 0
            )
          ),
          cache_info = list(
            cached = FALSE,
            cache_key = cache_key,
            analysis_time = Sys.time()
          )
        )

        # 9. 保存到缓存（如果启用）
        if (use_cache && !is.null(isotope_cache) && !is.null(cache_key)) {
          isotope_cache$save_to_cache(cache_key, results)
        }

        return(results)

      }, error = function(e) {
        log_error(paste("同位素内标监控分析失败:", e$message))
        return(list(
          success = FALSE,
          message = paste("分析失败:", e$message),
          data = NULL
        ))
      })
    },

    # 按化合物和文件分析峰形
    analyze_peaks_by_compound_and_file = function(isotope_data) {
      if (is.null(isotope_data) || !is.data.frame(isotope_data) ||
          is.na(nrow(isotope_data)) || nrow(isotope_data) == 0) {
        return(list())
      }

      results <- list()

      # 按化合物和文件分组
      compound_file_groups <- split(isotope_data,
                                   list(isotope_data$compound_name, isotope_data$file_name),
                                   drop = TRUE)

      for (group_name in names(compound_file_groups)) {
        group_data <- compound_file_groups[[group_name]]

        if (nrow(group_data) > 0) {
          analysis_result <- analyzer$analyze_compound_peak(group_data)
          if (!is.null(analysis_result)) {
            results[[length(results) + 1]] <- analysis_result
          }
        }
      }

      return(results)
    },

    # 创建同位素内标信息表格
    create_isotope_info_table = function(peak_analysis_results) {
      if (is.null(peak_analysis_results) || length(peak_analysis_results) == 0) {
        return(data.frame())
      }

      # 提取表格数据
      table_data <- data.frame(
        化合物名称 = character(),
        样本名称 = character(),
        保留时间_min = numeric(),
        实测mz = numeric(),
        理论mz = numeric(),
        mz偏差_ppm = numeric(),
        mz扫描范围 = character(),
        峰强度 = numeric(),
        峰形质量 = numeric(),
        半峰宽_min = numeric(),
        峰面积 = numeric(),
        信噪比 = numeric(),
        数据点数 = integer(),
        非噪音点数 = integer(),
        MS2状态 = character(),
        离子类型 = character(),
        质量等级 = character(),
        质量问题 = character(),
        stringsAsFactors = FALSE
      )

      for (result in peak_analysis_results) {
        if (result$success) {
          # 格式化m/z扫描范围
          mz_range_str <- paste0(
            round(result$mz_range$min_mz, 4), " - ",
            round(result$mz_range$max_mz, 4),
            " (", round(result$mz_range$range_width, 4), ")"
          )

          # 格式化质量问题
          quality_issues <- ""
          quality_grade <- "优秀"
          if (result$has_quality_issues && length(result$quality_flags) > 0) {
            issues_list <- names(result$quality_flags)
            # 翻译质量问题标志
            issue_translations <- c(
              "retention_time_outlier" = "保留时间异常",
              "intensity_outlier" = "强度异常",
              "mz_deviation_outlier" = "质量偏差异常",
              "snr_outlier" = "信噪比异常",
              "fwhm_outlier" = "峰宽异常",
              "poor_peak_shape" = "峰形质量差",
              "low_snr" = "信噪比低"
            )
            translated_issues <- sapply(issues_list, function(x) issue_translations[x] %||% x)
            quality_issues <- paste(translated_issues, collapse = "; ")

            # 根据问题数量确定质量等级
            if (length(issues_list) >= 3) {
              quality_grade <- "差"
            } else if (length(issues_list) >= 2) {
              quality_grade <- "一般"
            } else {
              quality_grade <- "良好"
            }
          }

          # 确定MS2状态
          ms2_status <- "未知"
          if (!is.null(result$has_ms2)) {
            ms2_status <- ifelse(result$has_ms2, "有MS2", "无MS2")
          }

          # 计算理论m/z（从实测m/z和偏差反推）
          theoretical_mz <- NA
          if (!is.na(result$peak_mz) && !is.na(result$mz_deviation_ppm)) {
            theoretical_mz <- result$peak_mz / (1 + result$mz_deviation_ppm / 1e6)
          }

          # 添加行数据
          new_row <- data.frame(
            化合物名称 = result$compound_name %||% "",
            样本名称 = result$file_name %||% "",
            保留时间_min = round(result$retention_time %||% NA, 3),
            实测mz = round(result$peak_mz %||% NA, 4),
            理论mz = round(theoretical_mz %||% NA, 4),
            mz偏差_ppm = round(result$mz_deviation_ppm %||% NA, 2),
            mz扫描范围 = mz_range_str,
            峰强度 = round(result$peak_intensity %||% NA, 0),
            峰形质量 = round(result$peak_shape_quality %||% NA, 3),
            半峰宽_min = round(result$fwhm %||% NA, 3),
            峰面积 = round(result$peak_area %||% NA, 0),
            信噪比 = round(result$snr %||% NA, 1),
            数据点数 = result$total_points %||% 0,
            非噪音点数 = result$non_noise_points %||% 0,
            MS2状态 = ms2_status,
            离子类型 = result$ion_type %||% "[M+H]+",
            质量等级 = quality_grade,
            质量问题 = quality_issues,
            stringsAsFactors = FALSE
          )

          table_data <- rbind(table_data, new_row)
        }
      }

      return(table_data)
    },

    # 获取MS2谱图数据
    get_ms2_spectrum = function(spectrum_id) {
      return(matcher$get_ms2_spectrum_data(spectrum_id))
    }
  )
)

# 结果缓存管理器
IsotopeResultsCache <- setRefClass("IsotopeResultsCache",
  fields = list(
    cache_dir = "character",
    max_cache_size = "numeric",
    cache_expiry_hours = "numeric"
  ),
  methods = list(
    initialize = function(cache_directory = "cache/isotope_results", max_size_mb = 100, expiry_hours = 24) {
      cache_dir <<- cache_directory
      max_cache_size <<- max_size_mb * 1024 * 1024  # 转换为字节
      cache_expiry_hours <<- expiry_hours

      # 创建缓存目录
      if (!dir.exists(cache_dir)) {
        dir.create(cache_dir, recursive = TRUE)
      }
    },

    # 生成缓存键
    generate_cache_key = function(file_ids, config_hash, tolerance_ppm) {
      key_string <- paste(
        paste(sort(file_ids), collapse = "_"),
        config_hash,
        tolerance_ppm,
        sep = "_"
      )
      return(digest::digest(key_string, algo = "md5"))
    },

    # 获取缓存文件路径
    get_cache_path = function(cache_key) {
      return(file.path(cache_dir, paste0(cache_key, ".rds")))
    },

    # 检查缓存是否存在且有效
    is_cache_valid = function(cache_key) {
      cache_path <- get_cache_path(cache_key)
      if (!file.exists(cache_path)) {
        return(FALSE)
      }

      # 检查缓存是否过期
      cache_time <- file.mtime(cache_path)
      current_time <- Sys.time()
      age_hours <- as.numeric(difftime(current_time, cache_time, units = "hours"))

      return(age_hours < cache_expiry_hours)
    },

    # 保存结果到缓存
    save_to_cache = function(cache_key, results) {
      tryCatch({
        cache_path <- get_cache_path(cache_key)
        saveRDS(results, cache_path)
        log_info(paste("分析结果已缓存:", cache_key))

        # 清理过期缓存
        cleanup_expired_cache()

        return(TRUE)
      }, error = function(e) {
        log_warning(paste("缓存保存失败:", e$message))
        return(FALSE)
      })
    },

    # 从缓存加载结果
    load_from_cache = function(cache_key) {
      tryCatch({
        if (is_cache_valid(cache_key)) {
          cache_path <- get_cache_path(cache_key)
          results <- readRDS(cache_path)
          log_info(paste("从缓存加载结果:", cache_key))
          return(results)
        }
        return(NULL)
      }, error = function(e) {
        log_warning(paste("缓存加载失败:", e$message))
        return(NULL)
      })
    },

    # 清理过期缓存
    cleanup_expired_cache = function() {
      tryCatch({
        cache_files <- list.files(cache_dir, pattern = "\\.rds$", full.names = TRUE)
        current_time <- Sys.time()

        for (cache_file in cache_files) {
          cache_time <- file.mtime(cache_file)
          age_hours <- as.numeric(difftime(current_time, cache_time, units = "hours"))

          if (age_hours > cache_expiry_hours) {
            file.remove(cache_file)
            log_info(paste("删除过期缓存:", basename(cache_file)))
          }
        }

        # 检查缓存大小并清理最旧的文件
        total_size <- sum(file.size(list.files(cache_dir, pattern = "\\.rds$", full.names = TRUE)))
        if (total_size > max_cache_size) {
          cache_files <- list.files(cache_dir, pattern = "\\.rds$", full.names = TRUE)
          file_info <- data.frame(
            path = cache_files,
            mtime = sapply(cache_files, file.mtime),
            size = sapply(cache_files, file.size)
          )
          file_info <- file_info[order(file_info$mtime), ]

          current_size <- total_size
          for (i in 1:nrow(file_info)) {
            if (current_size <= max_cache_size) break
            file.remove(file_info$path[i])
            current_size <- current_size - file_info$size[i]
            log_info(paste("删除缓存文件以释放空间:", basename(file_info$path[i])))
          }
        }
      }, error = function(e) {
        log_warning(paste("缓存清理失败:", e$message))
      })
    }
  )
)

# 全局处理器实例
isotope_processor <- NULL
isotope_cache <- NULL

# 初始化同位素监控处理器
initialize_isotope_processor <- function(database_path, config_path = NULL, tolerance_ppm = 10) {
  isotope_processor <<- IsotopeMonitoringProcessor$new(database_path, config_path, tolerance_ppm)
  isotope_cache <<- IsotopeResultsCache$new()
  log_info("同位素监控处理器已初始化")
  return(TRUE)
}

# 获取处理器实例
get_isotope_processor <- function() {
  return(isotope_processor)
}

# 获取缓存实例
get_isotope_cache <- function() {
  return(isotope_cache)
}

# 集成测试函数
test_isotope_monitoring_integration <- function(database_path, config_path = NULL) {
  tryCatch({
    log_info("开始同位素监控模块集成测试...")

    # 1. 初始化处理器
    if (!initialize_isotope_processor(database_path, config_path)) {
      stop("处理器初始化失败")
    }

    processor <- get_isotope_processor()
    if (is.null(processor)) {
      stop("无法获取处理器实例")
    }

    # 2. 测试配置加载
    log_info("测试配置加载...")
    if (!processor$extractor$load_config_from_monitor_ions()) {
      stop("配置加载失败")
    }

    # 3. 测试数据提取
    log_info("测试数据提取...")
    test_data <- processor$extractor$extract_all_compounds_data(file_ids = NULL)
    if (is.null(test_data) || !is.data.frame(test_data) ||
        is.na(nrow(test_data)) || nrow(test_data) == 0) {
      log_warning("没有找到测试数据，但这可能是正常的")
      return(list(
        success = TRUE,
        message = "集成测试完成（无数据）",
        tests_passed = c("初始化", "配置加载"),
        tests_failed = c(),
        data_available = FALSE
      ))
    }

    # 4. 测试完整分析流程
    log_info("测试完整分析流程...")
    analysis_results <- processor$process_isotope_monitoring(
      file_ids = NULL,
      include_ms2_matching = TRUE,
      use_cache = TRUE
    )

    if (!analysis_results$success) {
      stop(paste("分析流程失败:", analysis_results$message))
    }

    # 5. 测试可视化组件
    log_info("测试可视化组件...")
    tests_passed <- c("初始化", "配置加载", "数据提取", "分析流程")
    tests_failed <- c()

    # 测试3D散点图
    if (!is.null(analysis_results$data$scatter_plot)) {
      tests_passed <- c(tests_passed, "3D散点图")
    } else {
      tests_failed <- c(tests_failed, "3D散点图")
    }

    # 测试信息表格
    if (!is.null(analysis_results$data$info_table) && nrow(analysis_results$data$info_table) > 0) {
      tests_passed <- c(tests_passed, "信息表格")
    } else {
      tests_failed <- c(tests_failed, "信息表格")
    }

    # 6. 测试MS2谱图功能（如果有MS2数据）
    if (!is.null(analysis_results$data$raw_data) &&
        "has_ms2" %in% colnames(analysis_results$data$raw_data) &&
        any(analysis_results$data$raw_data$has_ms2, na.rm = TRUE)) {

      ms2_rows <- which(analysis_results$data$raw_data$has_ms2)
      if (length(ms2_rows) > 0) {
        test_spectrum_id <- analysis_results$data$raw_data$spectrum_id[ms2_rows[1]]
        ms2_data <- processor$get_ms2_spectrum(test_spectrum_id)

        if (!is.null(ms2_data)) {
          tests_passed <- c(tests_passed, "MS2谱图数据")

          # 测试MS2谱图可视化
          ms2_plot <- processor$visualizer$create_ms2_spectrum_plot(ms2_data)
          if (!is.null(ms2_plot)) {
            tests_passed <- c(tests_passed, "MS2谱图可视化")
          } else {
            tests_failed <- c(tests_failed, "MS2谱图可视化")
          }
        } else {
          tests_failed <- c(tests_failed, "MS2谱图数据")
        }
      }
    }

    # 7. 测试缓存功能
    log_info("测试缓存功能...")
    cache <- get_isotope_cache()
    if (!is.null(cache)) {
      tests_passed <- c(tests_passed, "缓存初始化")

      # 测试缓存保存和加载
      test_cache_key <- "test_key_123"
      test_data_for_cache <- list(test = "data")

      if (cache$save_to_cache(test_cache_key, test_data_for_cache)) {
        cached_data <- cache$load_from_cache(test_cache_key)
        if (!is.null(cached_data) && identical(cached_data$test, "data")) {
          tests_passed <- c(tests_passed, "缓存功能")
        } else {
          tests_failed <- c(tests_failed, "缓存功能")
        }
      } else {
        tests_failed <- c(tests_failed, "缓存功能")
      }
    } else {
      tests_failed <- c(tests_failed, "缓存初始化")
    }

    log_info("同位素监控模块集成测试完成")

    return(list(
      success = length(tests_failed) == 0,
      message = paste("集成测试完成，通过", length(tests_passed), "项，失败", length(tests_failed), "项"),
      tests_passed = tests_passed,
      tests_failed = tests_failed,
      data_available = TRUE,
      analysis_summary = analysis_results$data$summary
    ))

  }, error = function(e) {
    log_error(paste("集成测试失败:", e$message))
    return(list(
      success = FALSE,
      message = paste("集成测试失败:", e$message),
      tests_passed = c(),
      tests_failed = c("集成测试"),
      data_available = FALSE
    ))
  })
}

# 同位素内标监控服务器逻辑
isotope_monitoring_server <- function(input, output, session) {

  # 响应式变量
  isotope_analysis_results <- reactiveVal(NULL)
  isotope_processor_initialized <- reactiveVal(FALSE)

  # 更新系统状态显示
  observe({
    status <- isotope_processor_initialized()
    project_root <- get_project_root_path()

    if (exists("shinyjs")) {
      if (is.null(project_root) || project_root == "") {
        # 未选择项目
        shinyjs::html("isotope_processor_status", "未选择项目")
        shinyjs::removeClass("isotope_system_status", "alert-success")
        shinyjs::removeClass("isotope_system_status", "alert-warning")
        shinyjs::removeClass("isotope_system_status", "alert-danger")
        shinyjs::addClass("isotope_system_status", "alert-info")
      } else if (status) {
        # 已初始化
        shinyjs::html("isotope_processor_status", "已初始化 ✓")
        shinyjs::removeClass("isotope_system_status", "alert-warning")
        shinyjs::removeClass("isotope_system_status", "alert-danger")
        shinyjs::removeClass("isotope_system_status", "alert-info")
        shinyjs::addClass("isotope_system_status", "alert-success")
      } else {
        # 项目已选择但未初始化（可能是数据库不存在或为空）
        db_path <- file.path(project_root, "results", "spectra.db")
        if (!file.exists(db_path)) {
          shinyjs::html("isotope_processor_status", "数据库文件不存在")
        } else {
          shinyjs::html("isotope_processor_status", "初始化失败 ✗")
        }
        shinyjs::removeClass("isotope_system_status", "alert-success")
        shinyjs::removeClass("isotope_system_status", "alert-info")
        shinyjs::addClass("isotope_system_status", "alert-warning")
      }
    }
  })

  # 监听项目变化，当项目导入或切换时初始化处理器
  # 使用响应式变量来跟踪项目路径变化
  current_project_path <- reactiveVal(NULL)

  # 监听项目路径变化
  observe({
    project_root <- get_project_root_path()

    # 只有当项目路径真正变化时才执行初始化
    if (!identical(current_project_path(), project_root)) {
      current_project_path(project_root)

      # 重置处理器状态
      isotope_processor_initialized(FALSE)

      if (!is.null(project_root) && project_root != "") {
        db_path <- file.path(project_root, "results", "spectra.db")

        if (file.exists(db_path)) {
          tryCatch({
            log_info("项目变化，开始初始化同位素监控处理器...")
            log_info(paste("项目路径:", project_root))
            log_info(paste("数据库路径:", db_path))

            # 检查数据库连接和表结构
            con <- DBI::dbConnect(RSQLite::SQLite(), db_path)
            tables <- DBI::dbListTables(con)
            DBI::dbDisconnect(con)

            if (length(tables) == 0) {
              log_warning("数据库为空，同位素监控处理器暂不可用")
              isotope_processor_initialized(FALSE)
              return()
            }

            # 重置全局处理器（清除之前项目的缓存）
            isotope_processor <<- NULL
            isotope_cache <<- NULL

            # 初始化处理器
            if (initialize_isotope_processor(db_path)) {
              isotope_processor_initialized(TRUE)
              log_info("同位素监控处理器初始化成功")
            } else {
              log_error("同位素监控处理器初始化失败")
              isotope_processor_initialized(FALSE)
            }
          }, error = function(e) {
            log_error(paste("初始化同位素监控处理器失败:", e$message))
            isotope_processor_initialized(FALSE)
          })
        } else {
          log_info(paste("项目数据库文件不存在，同位素监控处理器暂不可用:", db_path))
          isotope_processor_initialized(FALSE)
        }
      } else {
        log_debug("未选择项目，同位素监控处理器暂不可用")
        isotope_processor_initialized(FALSE)
      }
    }
  })

  # 手动重新初始化处理器
  observeEvent(input$reinit_isotope_processor, {
    log_info("用户请求重新初始化同位素监控处理器")

    # 更新状态为初始化中
    if (exists("shinyjs")) {
      shinyjs::html("isotope_processor_status", "初始化中...")
      shinyjs::removeClass("isotope_system_status", "alert-success")
      shinyjs::removeClass("isotope_system_status", "alert-warning")
      shinyjs::removeClass("isotope_system_status", "alert-danger")
      shinyjs::addClass("isotope_system_status", "alert-info")
    }

    project_root <- get_project_root_path()
    if (is.null(project_root) || project_root == "") {
      log_warning("未选择项目，无法初始化同位素监控处理器")
      isotope_processor_initialized(FALSE)
      return()
    }

    db_path <- file.path(project_root, "results", "spectra.db")
    if (!file.exists(db_path)) {
      log_warning(paste("项目数据库文件不存在:", db_path))
      isotope_processor_initialized(FALSE)
      return()
    }

    tryCatch({
      log_info("手动重新初始化同位素监控处理器...")
      log_info(paste("项目路径:", project_root))
      log_info(paste("数据库路径:", db_path))

      # 检查数据库连接和表结构
      con <- DBI::dbConnect(RSQLite::SQLite(), db_path)
      tables <- DBI::dbListTables(con)
      DBI::dbDisconnect(con)

      if (length(tables) == 0) {
        log_warning("数据库为空，无法初始化同位素监控处理器")
        isotope_processor_initialized(FALSE)
        return()
      }

      # 重置全局处理器
      isotope_processor <<- NULL
      isotope_cache <<- NULL

      # 重新初始化
      if (initialize_isotope_processor(db_path)) {
        isotope_processor_initialized(TRUE)
        log_info("手动重新初始化成功")
      } else {
        isotope_processor_initialized(FALSE)
        log_error("手动重新初始化失败")
      }
    }, error = function(e) {
      log_error(paste("手动重新初始化失败:", e$message))
      isotope_processor_initialized(FALSE)
    })
  })

  # 更新文件选择列表
  observe({
    if (isotope_processor_initialized()) {
      project_root <- get_project_root_path()
      if (!is.null(project_root)) {
        db_path <- file.path(project_root, "results", "spectra.db")
        if (file.exists(db_path)) {
          tryCatch({
            con <- DBI::dbConnect(RSQLite::SQLite(), db_path)
            files <- DBI::dbGetQuery(con, "SELECT DISTINCT file_id, file_name FROM data_files ORDER BY file_name")
            DBI::dbDisconnect(con)

            if (nrow(files) > 0) {
              choices <- as.list(files$file_id)
              names(choices) <- files$file_name
              choices <- c(list("所有文件" = "all"), choices)

              updateSelectInput(session, "isotope_file_selection",
                               choices = choices,
                               selected = "all")
            }
          }, error = function(e) {
            log_error(paste("更新文件列表失败:", e$message))
          })
        }
      }
    }
  })

  # 运行同位素内标分析
  observeEvent(input$run_isotope_analysis, {
    if (!isotope_processor_initialized()) {
      log_error("同位素监控处理器未初始化，无法执行分析")
      # 更新UI状态显示
      if (exists("shinyjs")) {
        shinyjs::html("isotope_analysis_status", "错误：同位素监控处理器未初始化")
        shinyjs::removeClass("isotope_analysis_status", "alert-success")
        shinyjs::addClass("isotope_analysis_status", "alert-danger")
      }
      return()
    }

    # 显示分析状态
    if (exists("shinyjs")) {
      shinyjs::show("isotope_analysis_status")
      shinyjs::html("isotope_analysis_status", "正在运行同位素内标分析...")
      shinyjs::removeClass("isotope_analysis_status", "alert-info alert-success alert-danger")
      shinyjs::addClass("isotope_analysis_status", "alert-info")
    }

    # 准备参数
    file_ids <- NULL
    if (!"all" %in% input$isotope_file_selection) {
      file_ids <- input$isotope_file_selection
    }

    tolerance_ppm <- input$isotope_mass_tolerance
    include_ms2 <- input$isotope_include_ms2

    # 执行分析
    tryCatch({
      processor <- get_isotope_processor()
      if (!is.null(processor)) {
        processor$extractor$mass_tolerance_ppm <- tolerance_ppm
        result <- processor$process_isotope_monitoring(file_ids, include_ms2)

        if (result$success) {
          isotope_analysis_results(result$data)

          # 更新状态显示
          if (exists("shinyjs")) {
            shinyjs::html("isotope_analysis_status", paste("分析完成:", result$message))
            shinyjs::removeClass("isotope_analysis_status", "alert-info alert-danger")
            shinyjs::addClass("isotope_analysis_status", "alert-success")
          }

          log_info("同位素内标分析完成")
        } else {
          if (exists("shinyjs")) {
            shinyjs::html("isotope_analysis_status", paste("分析失败:", result$message))
            shinyjs::removeClass("isotope_analysis_status", "alert-info alert-success")
            shinyjs::addClass("isotope_analysis_status", "alert-danger")
          }

          log_error(paste("同位素内标分析失败:", result$message))
        }
      } else {
        log_error("同位素监控处理器未初始化，无法执行分析")
      }
    }, error = function(e) {
      log_error(paste("同位素内标分析过程中发生错误:", e$message))
    })
  })

  # 渲染3D散点图
  output$isotope_3d_scatter <- renderPlotly({
    results <- isotope_analysis_results()
    if (is.null(results) || is.null(results$scatter_plot)) {
      return(plotly_empty() %>%
             layout(title = "请先运行同位素内标分析"))
    }

    return(results$scatter_plot)
  })

  # 更新MS2谱图选择列表
  observe({
    results <- isotope_analysis_results()
    if (!is.null(results) && !is.null(results$raw_data)) {
      # 查找有MS2数据的点
      ms2_data <- results$raw_data[results$raw_data$has_ms2 == TRUE, ]

      if (nrow(ms2_data) > 0) {
        choices <- list()
        for (i in 1:nrow(ms2_data)) {
          row <- ms2_data[i, ]
          spectrum_ids <- strsplit(row$ms2_spectrum_ids, ",")[[1]]

          for (spectrum_id in spectrum_ids) {
            label <- paste0(row$compound_name, " - ", row$file_name,
                           " (RT: ", round(row$rtime, 2), "min)")
            choices[[label]] <- spectrum_id
          }
        }

        updateSelectInput(session, "ms2_spectrum_selection",
                         choices = choices)
      }
    }
  })

  # 加载MS2谱图
  observeEvent(input$load_ms2_spectrum, {
    spectrum_id <- input$ms2_spectrum_selection
    if (is.null(spectrum_id) || spectrum_id == "") {
      log_warning("用户未选择MS2谱图")
      return()
    }

    tryCatch({
      processor <- get_isotope_processor()
      if (!is.null(processor)) {
        ms2_data <- processor$get_ms2_spectrum(as.numeric(spectrum_id))

        if (!is.null(ms2_data) && !is.null(ms2_data$peaks) && nrow(ms2_data$peaks) > 0) {
          output$isotope_ms2_spectrum <- renderPlotly({
            processor$visualizer$create_ms2_spectrum_plot(ms2_data)
          })
        } else {
          log_error("无法加载MS2谱图数据")
        }
      }
    }, error = function(e) {
      log_error(paste("加载MS2谱图失败:", e$message))
    })
  })

  # 渲染信息表格
  output$isotope_info_table <- DT::renderDataTable({
    results <- isotope_analysis_results()
    if (is.null(results) || is.null(results$info_table)) {
      return(data.frame(消息 = "请先运行同位素内标分析"))
    }

    table_data <- results$info_table

    # 为有质量问题的行添加样式
    if (nrow(table_data) > 0) {
      dt <- DT::datatable(
        table_data,
        options = list(
          pageLength = 25,
          scrollX = TRUE,
          dom = 'Bfrtip',
          buttons = c('copy', 'csv', 'excel'),
          columnDefs = list(
            list(targets = "_all", className = "dt-center"),
            list(targets = c("保留时间_min", "实测mz", "理论mz", "mz偏差_ppm", "峰强度", "峰形质量", "半峰宽_min", "峰面积", "信噪比"), className = "dt-right")
          )
        ),
        extensions = 'Buttons',
        rownames = FALSE,
        filter = 'top'
      )

      # 根据质量等级添加行颜色
      if ("质量等级" %in% colnames(table_data)) {
        dt <- dt %>%
          DT::formatStyle(
            columns = 1:ncol(table_data),
            valueColumns = "质量等级",
            backgroundColor = DT::styleEqual(
              c("优秀", "良好", "一般", "差"),
              c("rgba(144, 238, 144, 0.3)", "rgba(173, 216, 230, 0.3)", "rgba(255, 255, 224, 0.5)", "rgba(255, 182, 193, 0.5)")
            )
          )
      }

      # 格式化数值列
      numeric_columns <- c("保留时间_min", "实测mz", "理论mz", "mz偏差_ppm", "峰形质量", "半峰宽_min", "信噪比")
      existing_numeric_columns <- intersect(numeric_columns, colnames(table_data))
      if (length(existing_numeric_columns) > 0) {
        dt <- dt %>% DT::formatRound(columns = existing_numeric_columns, digits = 3)
      }

      # 格式化大数值列（峰强度、峰面积）
      large_number_columns <- c("峰强度", "峰面积")
      existing_large_columns <- intersect(large_number_columns, colnames(table_data))
      if (length(existing_large_columns) > 0) {
        dt <- dt %>% DT::formatCurrency(columns = existing_large_columns, currency = "", digits = 0)
      }

      # 为m/z偏差列添加条件格式
      if ("mz偏差_ppm" %in% colnames(table_data)) {
        dt <- dt %>%
          DT::formatStyle(
            "mz偏差_ppm",
            backgroundColor = DT::styleInterval(
              cuts = c(-10, -5, 5, 10),
              values = c("rgba(255, 0, 0, 0.3)", "rgba(255, 165, 0, 0.3)", "rgba(255, 255, 255, 0)", "rgba(255, 165, 0, 0.3)", "rgba(255, 0, 0, 0.3)")
            )
          )
      }

      # 为信噪比列添加条件格式
      if ("信噪比" %in% colnames(table_data)) {
        dt <- dt %>%
          DT::formatStyle(
            "信噪比",
            backgroundColor = DT::styleInterval(
              cuts = c(5, 10, 50),
              values = c("rgba(255, 0, 0, 0.3)", "rgba(255, 165, 0, 0.3)", "rgba(255, 255, 255, 0)", "rgba(144, 238, 144, 0.3)")
            )
          )
      }

      return(dt)
    } else {
      return(DT::datatable(table_data, options = list(pageLength = 25)))
    }
  })

  # 渲染质量控制图表
  output$isotope_quality_plot <- renderPlotly({
    results <- isotope_analysis_results()
    if (is.null(results) || is.null(results$info_table)) {
      return(plotly_empty() %>%
             layout(title = "请先运行同位素内标分析"))
    }

    table_data <- results$info_table

    if (nrow(table_data) > 0) {
      # 创建质量控制散点图（保留时间 vs m/z偏差）
      p <- plot_ly(
        data = table_data,
        x = ~保留时间_min,
        y = ~mz偏差_ppm,
        color = ~质量问题 != "",
        colors = c("green", "red"),
        text = ~paste(
          "化合物:", 化合物名称, "<br>",
          "样本:", 样本名称, "<br>",
          "保留时间:", 保留时间_min, "min<br>",
          "m/z偏差:", mz偏差_ppm, "ppm<br>",
          "信噪比:", 信噪比, "<br>",
          if (质量问题 != "") paste("问题:", 质量问题) else "正常"
        ),
        hovertemplate = "%{text}<extra></extra>",
        type = "scatter",
        mode = "markers",
        marker = list(size = 8)
      ) %>%
      layout(
        title = "质量控制图 - 保留时间 vs m/z偏差",
        xaxis = list(title = "保留时间 (min)"),
        yaxis = list(title = "m/z偏差 (ppm)"),
        showlegend = TRUE
      )

      return(p)
    } else {
      return(plotly_empty() %>%
             layout(title = "没有数据可显示"))
    }
  })

  # 更新质量问题摘要
  observe({
    results <- isotope_analysis_results()
    if (!is.null(results) && !is.null(results$info_table)) {
      table_data <- results$info_table

      if (nrow(table_data) > 0) {
        total_samples <- nrow(table_data)
        problem_samples <- sum(table_data$质量问题 != "")
        normal_samples <- total_samples - problem_samples

        # 统计各种问题类型
        all_problems <- table_data$质量问题[table_data$质量问题 != ""]
        problem_types <- unlist(strsplit(all_problems, "; "))
        problem_counts <- table(problem_types)

        summary_html <- paste0(
          "<div class='row'>",
          "<div class='col-md-6'>",
          "<h6>总体统计</h6>",
          "<ul>",
          "<li>总样本数: ", total_samples, "</li>",
          "<li>正常样本: ", normal_samples, " (", round(normal_samples/total_samples*100, 1), "%)</li>",
          "<li>问题样本: ", problem_samples, " (", round(problem_samples/total_samples*100, 1), "%)</li>",
          "</ul>",
          "</div>",
          "<div class='col-md-6'>",
          "<h6>问题类型统计</h6>",
          "<ul>"
        )

        if (length(problem_counts) > 0) {
          for (i in 1:length(problem_counts)) {
            problem_name <- names(problem_counts)[i]
            problem_count <- problem_counts[i]
            summary_html <- paste0(summary_html,
                                  "<li>", problem_name, ": ", problem_count, "</li>")
          }
        } else {
          summary_html <- paste0(summary_html, "<li>无质量问题</li>")
        }

        summary_html <- paste0(summary_html, "</ul></div></div>")

        if (exists("shinyjs")) {
          shinyjs::html("quality_issues_summary", summary_html)
        }
      }
    }
  })

  # 下载结果
  output$download_isotope_results <- downloadHandler(
    filename = function() {
      paste0("isotope_monitoring_results_", Sys.Date(), ".xlsx")
    },
    content = function(file) {
      results <- isotope_analysis_results()
      if (!is.null(results) && !is.null(results$info_table)) {
        # 使用openxlsx包创建Excel文件
        if (!requireNamespace("openxlsx", quietly = TRUE)) {
          install.packages("openxlsx")
        }

        wb <- openxlsx::createWorkbook()

        # 添加信息表格工作表
        openxlsx::addWorksheet(wb, "同位素内标信息")
        openxlsx::writeData(wb, "同位素内标信息", results$info_table)

        # 添加摘要工作表
        if (!is.null(results$summary)) {
          summary_df <- data.frame(
            项目 = c("监控化合物数", "分析样本数", "数据点总数", "MS2匹配点数"),
            数值 = c(results$summary$total_compounds,
                    results$summary$total_samples,
                    results$summary$total_data_points,
                    results$summary$ms2_matched_points)
          )
          openxlsx::addWorksheet(wb, "分析摘要")
          openxlsx::writeData(wb, "分析摘要", summary_df)
        }

        openxlsx::saveWorkbook(wb, file, overwrite = TRUE)
      }
    }
  )
}
